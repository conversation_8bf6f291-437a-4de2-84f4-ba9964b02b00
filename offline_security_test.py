#!/usr/bin/env python3
"""
离线环境安全测试脚本
演示和测试离线环境下的许可证安全机制
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_offline_time_validation():
    """测试离线时间验证机制"""
    print("=== 离线时间验证测试 ===")
    
    try:
        from core.offline_time_validator import offline_time_validator
        
        # 创建测试许可证时间
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        print("1. 正常时间验证")
        result = offline_time_validator.validate_license_time(start_date, end_date)
        
        print(f"   验证结果: {'✅ 通过' if result.is_valid else '❌ 失败'}")
        print(f"   安全评分: {result.security_score}/100")
        print(f"   置信度: {result.confidence_level}")
        
        if result.warnings:
            print("   警告信息:")
            for warning in result.warnings:
                print(f"     - {warning}")
        
        print("\n2. 时间源分析")
        evidence = result.evidence
        if 'time_sources' in evidence:
            print("   可用时间源:")
            for source, time_val in evidence['time_sources'].items():
                print(f"     - {source}: {time_val}")
        
        print(f"   异常评分: {evidence.get('anomaly_score', 'N/A')}")
        print(f"   锚点评分: {evidence.get('anchor_score', 'N/A')}")
        print(f"   完整性评分: {evidence.get('integrity_score', 'N/A')}")
        print(f"   证明链评分: {evidence.get('proof_score', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_offline_license_manager():
    """测试离线许可证管理器"""
    print("\n=== 离线许可证管理器测试 ===")
    
    try:
        from core.offline_license_manager import offline_license_manager, OfflineLicenseStatus
        
        print("1. 许可证加载测试")
        status, license_info = offline_license_manager.load_license()
        print(f"   许可证状态: {status}")
        
        if status == OfflineLicenseStatus.VALID and license_info:
            print(f"   组织: {license_info.organization}")
            print(f"   授权模块: {license_info.modules}")
            print(f"   执行次数: {license_info.current_executions}/{license_info.max_executions}")
            
            print("\n2. 安全报告")
            security_report = offline_license_manager.get_security_report()
            print(f"   安全评分: {security_report.get('security_score', 'N/A')}")
            print(f"   置信度: {security_report.get('confidence_level', 'N/A')}")
            print(f"   风险评估: {security_report.get('risk_assessment', {}).get('overall', 'N/A')}")
            
            if security_report.get('security_recommendations'):
                print("   安全建议:")
                for rec in security_report['security_recommendations'][:3]:  # 显示前3条
                    print(f"     - {rec}")
            
            print("\n3. 执行计数测试")
            old_count = license_info.current_executions
            success = offline_license_manager.increment_execution_count()
            
            if success:
                print(f"   ✅ 执行计数成功: {old_count} -> {old_count + 1}")
                
                # 重新加载验证
                status2, license_info2 = offline_license_manager.load_license()
                if license_info2 and license_info2.current_executions == old_count + 1:
                    print("   ✅ 计数持久化成功")
                else:
                    print("   ❌ 计数持久化失败")
            else:
                print("   ❌ 执行计数失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_time_tampering_detection():
    """测试时间篡改检测"""
    print("\n=== 时间篡改检测测试 ===")
    
    try:
        from core.offline_time_validator import offline_time_validator
        
        print("1. 基准时间验证")
        start_date = datetime.now().isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        baseline_result = offline_time_validator.validate_license_time(start_date, end_date)
        baseline_score = baseline_result.security_score
        print(f"   基准安全评分: {baseline_score}")
        
        print("\n2. 模拟时间异常")
        # 这里我们不能真正修改系统时间，但可以分析检测机制
        
        # 检查时间源一致性
        time_sources = offline_time_validator._get_multiple_time_sources()
        if len(time_sources) > 1:
            times = list(time_sources.values())
            timestamps = [t.timestamp() for t in times]
            max_diff = max(timestamps) - min(timestamps)
            print(f"   时间源差异: {max_diff:.2f} 秒")
            
            if max_diff > 60:
                print("   ⚠️  检测到时间源不一致")
            else:
                print("   ✅ 时间源一致性良好")
        
        # 检查系统完整性
        integrity_score = offline_time_validator._check_system_integrity()
        print(f"   系统完整性评分: {integrity_score}/20")
        
        # 检查时间证明链
        proof_score = offline_time_validator._verify_time_proof_chain(datetime.now())
        print(f"   时间证明链评分: {proof_score}/20")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_system_fingerprinting():
    """测试系统指纹识别"""
    print("\n=== 系统指纹识别测试 ===")
    
    try:
        from core.offline_time_validator import offline_time_validator
        
        print("1. 系统指纹生成")
        fingerprint = offline_time_validator._get_system_fingerprint()
        
        print("   系统信息:")
        print(f"     平台: {fingerprint.get('platform', 'N/A')}")
        print(f"     处理器: {fingerprint.get('processor', 'N/A')}")
        print(f"     CPU核心数: {fingerprint.get('cpu_count', 'N/A')}")
        print(f"     内存总量: {fingerprint.get('memory_total', 'N/A')} bytes")
        
        network_interfaces = fingerprint.get('network_interfaces', [])
        if network_interfaces:
            print(f"     网络接口: {len(network_interfaces)} 个")
            for iface in network_interfaces[:2]:  # 显示前2个
                print(f"       - {iface.get('interface', 'N/A')}: {iface.get('mac', 'N/A')}")
        
        print("\n2. 指纹一致性测试")
        fingerprint2 = offline_time_validator._get_system_fingerprint()
        similarity = offline_time_validator._compare_fingerprints(fingerprint, fingerprint2)
        print(f"   指纹相似度: {similarity:.2%}")
        
        if similarity > 0.95:
            print("   ✅ 系统指纹一致性优秀")
        elif similarity > 0.8:
            print("   ✅ 系统指纹一致性良好")
        else:
            print("   ⚠️  系统指纹一致性较差")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def demonstrate_offline_security_features():
    """演示离线安全特性"""
    print("\n=== 离线安全特性演示 ===")
    
    features = [
        {
            "name": "多源时间验证",
            "description": "从系统时间、硬件时钟、文件系统等多个源获取时间",
            "benefit": "提高时间准确性，降低单点篡改风险"
        },
        {
            "name": "时间证明链",
            "description": "建立本地时间证明的链式结构",
            "benefit": "防止时间回滚攻击，确保时间单调性"
        },
        {
            "name": "系统指纹识别",
            "description": "基于硬件和系统特征生成唯一指纹",
            "benefit": "检测环境变化，防止许可证迁移"
        },
        {
            "name": "异常检测算法",
            "description": "分析时间跳跃、系统状态等异常模式",
            "benefit": "主动发现潜在的安全威胁"
        },
        {
            "name": "安全评分机制",
            "description": "综合多个安全指标计算可信度评分",
            "benefit": "量化安全风险，支持风险决策"
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"\n{i}. {feature['name']}")
        print(f"   描述: {feature['description']}")
        print(f"   优势: {feature['benefit']}")

def show_offline_security_recommendations():
    """显示离线环境安全建议"""
    print("\n=== 离线环境安全建议 ===")
    
    recommendations = {
        "部署前准备": [
            "确保系统时间准确设置",
            "禁用不必要的时间同步服务",
            "备份系统关键文件时间戳",
            "记录硬件配置信息"
        ],
        "运行时监控": [
            "定期检查安全评分",
            "监控时间异常警告",
            "跟踪系统完整性变化",
            "审查执行日志"
        ],
        "安全加固": [
            "限制系统时间修改权限",
            "启用文件完整性监控",
            "配置安全审计日志",
            "实施访问控制策略"
        ],
        "应急响应": [
            "制定时间异常处理流程",
            "准备系统恢复方案",
            "建立安全事件上报机制",
            "定期进行安全演练"
        ]
    }
    
    for category, items in recommendations.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  • {item}")

def evaluate_offline_security_level():
    """评估离线环境安全等级"""
    print("\n=== 离线环境安全等级评估 ===")
    
    try:
        from core.offline_time_validator import offline_time_validator
        
        # 执行综合安全评估
        start_date = datetime.now().isoformat()
        end_date = (datetime.now() + timedelta(days=30)).isoformat()
        
        result = offline_time_validator.validate_license_time(start_date, end_date)
        score = result.security_score
        
        print(f"当前安全评分: {score}/100")
        
        if score >= 80:
            level = "高"
            description = "安全机制完善，可抵御大部分攻击"
        elif score >= 60:
            level = "中"
            description = "基本安全保障，建议加强监控"
        elif score >= 40:
            level = "低"
            description = "存在安全风险，需要立即改进"
        else:
            level = "很低"
            description = "严重安全隐患，不建议生产使用"
        
        print(f"安全等级: {level}")
        print(f"等级描述: {description}")
        
        # 详细分析
        evidence = result.evidence
        print("\n详细分析:")
        print(f"  时间源数量: {len(evidence.get('time_sources', {}))}")
        print(f"  异常检测: {evidence.get('anomaly_score', 0)}/100")
        print(f"  锚点验证: {evidence.get('anchor_score', 0)}/30")
        print(f"  系统完整性: {evidence.get('integrity_score', 0)}/20")
        print(f"  证明链: {evidence.get('proof_score', 0)}/20")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 评估失败: {e}")
        return False

def main():
    """主测试函数"""
    print("离线环境许可证安全测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("离线时间验证", test_offline_time_validation),
        ("离线许可证管理", test_offline_license_manager),
        ("时间篡改检测", test_time_tampering_detection),
        ("系统指纹识别", test_system_fingerprinting),
        ("安全等级评估", evaluate_offline_security_level),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    # 显示安全特性和建议
    demonstrate_offline_security_features()
    show_offline_security_recommendations()
    
    print("\n" + "=" * 60)
    print("离线安全测试完成")

if __name__ == '__main__':
    main()

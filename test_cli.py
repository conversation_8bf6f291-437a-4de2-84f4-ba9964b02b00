#!/usr/bin/env python3
"""
CLI框架测试脚本
用于验证新的Click框架是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有模块是否能正常导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试核心模块
        from core.registry import registry
        print("✓ core.registry 导入成功")
        
        from core.exceptions import CLIError
        print("✓ core.exceptions 导入成功")
        
        from core.cli_framework import cli_app
        print("✓ core.cli_framework 导入成功")
        
        # 测试功能模块
        from modules import data_cleaning
        print("✓ modules.data_cleaning 导入成功")
        
        from modules import translation
        print("✓ modules.translation 导入成功")
        
        from modules import summarization
        print("✓ modules.summarization 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_registry():
    """测试模块注册器"""
    print("\n=== 测试模块注册器 ===")
    
    try:
        from core.registry import registry
        
        # 检查已注册的模块
        modules = registry.list_modules()
        print(f"已注册模块: {modules}")
        
        for module_id in modules:
            module_info = registry.get_module(module_id)
            if module_info:
                print(f"  - {module_id}: {module_info.name} - {module_info.description}")
        
        return len(modules) > 0
        
    except Exception as e:
        print(f"✗ 注册器测试失败: {e}")
        return False

def test_license_system():
    """测试许可证系统"""
    print("\n=== 测试许可证系统 ===")
    
    try:
        from core.auth import PermissionManager
        from core.license_manager import LicenseStatus
        
        # 检查许可证状态
        status, license_info = PermissionManager.check_license_status()
        print(f"许可证状态: {status}")
        
        if status == LicenseStatus.VALID:
            print("✓ 许可证有效")
            authorized_modules = PermissionManager.get_authorized_modules()
            print(f"授权模块: {authorized_modules}")
        else:
            print(f"✗ 许可证问题: {PermissionManager.get_status_message()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 许可证系统测试失败: {e}")
        return False

def test_cli_framework():
    """测试CLI框架"""
    print("\n=== 测试CLI框架 ===")
    
    try:
        from core.cli_framework import CLIContext, DynamicCLI
        
        # 创建CLI上下文
        ctx = CLIContext()
        print(f"CLI上下文创建成功")
        print(f"许可证状态: {ctx.license_status}")
        print(f"授权模块: {ctx.authorized_modules}")
        
        # 创建动态CLI
        cli = DynamicCLI()
        print("✓ 动态CLI创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ CLI框架测试失败: {e}")
        return False

def create_test_data():
    """创建测试数据文件"""
    print("\n=== 创建测试数据 ===")
    
    try:
        # 创建测试CSV文件
        test_csv = "test_data.csv"
        with open(test_csv, 'w', encoding='utf-8') as f:
            f.write("name,age,city\n")
            f.write("张三,25,北京\n")
            f.write("李四,30,上海\n")
            f.write("王五,,广州\n")  # 空值测试
            f.write("张三,25,北京\n")  # 重复数据测试
        print(f"✓ 创建测试CSV文件: {test_csv}")
        
        # 创建测试文本文件
        test_txt = "test_text.txt"
        with open(test_txt, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文档。")
            f.write("它包含多个句子用于测试摘要功能。")
            f.write("人工智能技术正在快速发展。")
            f.write("机器学习和深度学习是其重要分支。")
            f.write("自然语言处理技术让计算机能够理解人类语言。")
            f.write("这项技术在很多领域都有广泛应用。")
        print(f"✓ 创建测试文本文件: {test_txt}")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建测试数据失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    print("1. 查看帮助:")
    print("   python main.py --help")
    
    print("\n2. 查看许可证信息:")
    print("   python main.py license-info")
    
    print("\n3. 数据清理:")
    print("   python main.py data_cleaning test_data.csv -o cleaned_data.csv")
    
    print("\n4. 文本翻译:")
    print("   python main.py translation \"Hello World\" --target zh")
    print("   python main.py translation -f test_text.txt -o translated.txt")
    
    print("\n5. 文本摘要:")
    print("   python main.py summarization test_text.txt -l 3")
    print("   python main.py summarization test_text.txt -l 2 --method hybrid")

def main():
    """主测试函数"""
    print("CLI框架升级测试")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("模块导入", test_imports),
        ("模块注册器", test_registry),
        ("许可证系统", test_license_system),
        ("CLI框架", test_cli_framework),
        ("测试数据创建", create_test_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！CLI框架升级成功！")
        show_usage_examples()
    else:
        print("\n⚠️  部分测试失败，请检查相关问题")
        
        # 提供故障排除建议
        print("\n故障排除建议:")
        print("1. 确保安装了所有依赖: pip install click pandas cryptography")
        print("2. 检查许可证文件是否存在: config/app.license")
        print("3. 确保所有模块文件都已正确创建")

if __name__ == '__main__':
    main()

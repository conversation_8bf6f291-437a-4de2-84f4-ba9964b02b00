#!/usr/bin/env python3
"""
配置安全测试脚本
测试配置文件的安全机制和防篡改能力
"""

import sys
import json
import tempfile
from pathlib import Path
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_secure_config_loading():
    """测试安全配置加载"""
    print("=== 安全配置加载测试 ===")
    
    try:
        from core.secure_config_manager import secure_config_manager
        
        print("1. 正常配置加载")
        config = secure_config_manager.load_secure_config()
        
        # 检查核心安全参数是否存在
        core_components = config.get("core_security_components", {})
        print(f"   核心安全组件: {list(core_components.keys())}")

        # 检查配置是否为硬编码
        is_hardcoded = config.get("hardcoded", False)
        print(f"   配置类型: {'硬编码' if is_hardcoded else '外部文件'}")

        # 检查强制安全设置
        time_validation = config.get("time_validation", {})
        min_score = time_validation.get("min_security_score", 0)
        print(f"   最低安全分数: {min_score}")

        # 检查安全等级
        security_enforcement = config.get("security_enforcement", {})
        security_levels = security_enforcement.get("security_levels", {})
        if security_levels:
            print("   安全等级配置:")
            for level, settings in security_levels.items():
                print(f"     {level}: 最低分数 {settings.get('min_score', 'N/A')}")

        # 验证硬编码配置无法被修改
        if is_hardcoded:
            print("   ✅ 核心安全配置已硬编码，无法被外部修改")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_hardcoded_config_immutability():
    """测试硬编码配置的不可变性"""
    print("\n=== 硬编码配置不可变性测试 ===")

    try:
        from core.secure_config_manager import SecureConfigManager

        print("1. 验证配置硬编码")
        manager = SecureConfigManager()
        config = manager.load_secure_config()

        is_hardcoded = config.get("hardcoded", False)
        if is_hardcoded:
            print("   ✅ 配置已硬编码")
        else:
            print("   ❌ 配置未硬编码")
            return False

        print("2. 验证核心安全组件强制启用")
        core_components = config.get("core_security_components", {})

        mandatory_components = ["time_proof_chain", "system_fingerprint", "anomaly_detection"]
        all_mandatory_enabled = True

        for component in mandatory_components:
            if component in core_components:
                enabled = core_components[component].get("enabled", False)
                mandatory = core_components[component].get("mandatory", False)
                print(f"   {component}: 启用={enabled}, 强制={mandatory}")

                if not (enabled and mandatory):
                    all_mandatory_enabled = False
            else:
                print(f"   ❌ 缺少核心组件: {component}")
                all_mandatory_enabled = False

        if all_mandatory_enabled:
            print("   ✅ 所有核心安全组件都已强制启用")
        else:
            print("   ❌ 部分核心安全组件未正确配置")
            return False

        print("3. 验证安全分数下限")
        enforcement = config.get("security_enforcement", {})
        min_absolute = enforcement.get("min_security_score_absolute", 0)

        if min_absolute >= 50:
            print(f"   ✅ 绝对最低安全分数: {min_absolute}")
        else:
            print(f"   ❌ 绝对最低安全分数过低: {min_absolute}")
            return False

        return True

    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_config_tampering_protection():
    """测试配置篡改保护"""
    print("\n=== 配置篡改保护测试 ===")
    
    try:
        from core.secure_config_manager import SecureConfigManager
        
        # 创建临时配置管理器
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_manager = SecureConfigManager(temp_dir)
            
            print("1. 创建受保护的配置文件")
            test_config = {
                "test_parameter": "original_value",
                "security_score": 80
            }
            
            config_file = Path(temp_dir) / "test_config.json"
            temp_manager._save_protected_config(config_file, test_config)
            print("   ✅ 配置文件创建成功")
            
            print("2. 验证完整性保护")
            loaded_config = temp_manager._load_protected_config(config_file)
            if loaded_config and loaded_config["test_parameter"] == "original_value":
                print("   ✅ 完整性验证通过")
            else:
                print("   ❌ 完整性验证失败")
                return False
            
            print("3. 模拟文件篡改")
            # 直接修改文件内容
            with open(config_file, 'r') as f:
                content = f.read()
            
            # 篡改配置内容
            tampered_content = content.replace('"original_value"', '"tampered_value"')
            with open(config_file, 'w') as f:
                f.write(tampered_content)
            
            # 尝试加载被篡改的配置
            tampered_config = temp_manager._load_protected_config(config_file)
            if tampered_config is None:
                print("   ✅ 篡改检测成功，拒绝加载")
            else:
                print("   ❌ 篡改检测失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_user_config_validation():
    """测试用户配置验证"""
    print("\n=== 用户配置验证测试 ===")
    
    try:
        from core.secure_config_manager import SecureConfigManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_manager = SecureConfigManager(temp_dir)
            
            print("1. 测试允许的用户配置")
            allowed_config = {
                "logging": {
                    "log_level": "DEBUG",
                    "enabled": False
                },
                "alerts": {
                    "enabled": True
                }
            }
            
            validated = temp_manager._validate_user_config(allowed_config)
            if "logging" in validated:
                print("   ✅ 允许的配置通过验证")
            else:
                print("   ⚠️  允许的配置被意外拒绝")
            
            print("2. 测试禁止的用户配置")
            forbidden_config = {
                "time_validation": {
                    "min_security_score": 1  # 尝试降低安全分数
                },
                "core_security_components": {
                    "time_proof_chain": {
                        "enabled": False  # 尝试禁用核心组件
                    }
                }
            }
            
            validated_forbidden = temp_manager._validate_user_config(forbidden_config)
            if not validated_forbidden:
                print("   ✅ 禁止的配置被正确拒绝")
            else:
                print("   ❌ 禁止的配置未被拒绝")
                print(f"   泄露的配置: {validated_forbidden}")
                return False
            
            print("3. 测试参数约束")
            constrained_config = {
                "time_validation": {
                    "max_time_jump_seconds": 10  # 过小的值
                }
            }
            
            validated_constrained = temp_manager._validate_user_config(constrained_config)
            if "time_validation" not in validated_constrained:
                print("   ✅ 参数约束正确执行")
            else:
                print("   ❌ 参数约束未生效")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_security_constraint_enforcement():
    """测试安全约束强制执行"""
    print("\n=== 安全约束强制执行测试 ===")
    
    try:
        from core.secure_config_manager import SecureConfigManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_manager = SecureConfigManager(temp_dir)
            
            print("1. 测试最低安全分数强制执行")
            low_security_config = {
                "time_validation": {
                    "min_security_score": 10  # 极低的安全分数
                },
                "security_levels": {
                    "high": {
                        "min_score": 20  # 极低的高等级分数
                    }
                }
            }
            
            enforced_config = temp_manager._apply_security_constraints(low_security_config)
            
            actual_min_score = enforced_config.get("time_validation", {}).get("min_security_score", 0)
            actual_high_score = enforced_config.get("security_levels", {}).get("high", {}).get("min_score", 0)
            
            if actual_min_score >= 50 and actual_high_score >= 50:
                print(f"   ✅ 安全分数已强制提升: {actual_min_score}, {actual_high_score}")
            else:
                print(f"   ❌ 安全分数强制执行失败: {actual_min_score}, {actual_high_score}")
                return False
            
            print("2. 测试核心组件强制启用")
            disabled_components_config = {
                "core_security_components": {
                    "time_proof_chain": {
                        "enabled": False,
                        "mandatory": True
                    },
                    "system_fingerprint": {
                        "enabled": False,
                        "mandatory": True
                    }
                }
            }
            
            enforced_config2 = temp_manager._apply_security_constraints(disabled_components_config)
            
            components = enforced_config2.get("core_security_components", {})
            all_enabled = all(
                comp.get("enabled", False) 
                for comp in components.values() 
                if comp.get("mandatory", False)
            )
            
            if all_enabled:
                print("   ✅ 核心组件强制启用成功")
            else:
                print("   ❌ 核心组件强制启用失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_attack_scenarios():
    """测试攻击场景"""
    print("\n=== 攻击场景测试 ===")
    
    attack_scenarios = [
        {
            "name": "降低安全阈值攻击",
            "description": "攻击者尝试修改配置文件降低安全要求",
            "config_changes": {
                "time_validation.min_security_score": 1,
                "security_levels.high.min_score": 1
            },
            "expected_result": "被系统强制修正到安全值"
        },
        {
            "name": "禁用安全组件攻击",
            "description": "攻击者尝试禁用关键安全检查",
            "config_changes": {
                "core_security_components.time_proof_chain.enabled": False,
                "core_security_components.system_fingerprint.enabled": False
            },
            "expected_result": "核心组件被强制启用"
        },
        {
            "name": "修改检测权重攻击",
            "description": "攻击者尝试降低异常检测敏感度",
            "config_changes": {
                "anomaly_detection.anomaly_score_weights.time_source_diff": 0.01,
                "anomaly_detection.time_source_max_diff_seconds": 86400
            },
            "expected_result": "参数被约束在安全范围内"
        },
        {
            "name": "配置文件替换攻击",
            "description": "攻击者尝试替换整个配置文件",
            "config_changes": "完全替换配置文件",
            "expected_result": "完整性验证失败，使用默认安全配置"
        }
    ]
    
    for i, scenario in enumerate(attack_scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   攻击方式: {scenario['description']}")
        print(f"   预期防护: {scenario['expected_result']}")
    
    print("\n   ✅ 所有攻击场景都有相应的防护机制")
    return True

def show_security_recommendations():
    """显示安全建议"""
    print("\n=== 配置安全建议 ===")
    
    recommendations = {
        "部署策略": [
            "将系统配置文件设置为只读权限",
            "使用文件完整性监控工具",
            "定期备份配置文件",
            "限制配置文件的访问权限"
        ],
        "用户配置管理": [
            "只允许用户修改非安全相关参数",
            "对用户配置进行严格验证",
            "记录所有配置修改操作",
            "提供配置重置功能"
        ],
        "监控和审计": [
            "监控配置文件的修改",
            "记录配置验证失败事件",
            "定期检查配置完整性",
            "建立配置异常告警机制"
        ],
        "应急响应": [
            "准备最小安全配置",
            "建立配置恢复流程",
            "制定配置篡改应对方案",
            "定期进行配置安全演练"
        ]
    }
    
    for category, items in recommendations.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  • {item}")

def main():
    """主测试函数"""
    print("配置文件安全机制测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("安全配置加载", test_secure_config_loading),
        ("硬编码配置不可变性", test_hardcoded_config_immutability),
        ("配置篡改保护", test_config_tampering_protection),
        ("用户配置验证", test_user_config_validation),
        ("安全约束强制执行", test_security_constraint_enforcement),
        ("攻击场景分析", test_attack_scenarios),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    # 显示安全建议
    show_security_recommendations()
    
    print("\n" + "=" * 60)
    print("配置安全测试完成")

if __name__ == '__main__':
    main()

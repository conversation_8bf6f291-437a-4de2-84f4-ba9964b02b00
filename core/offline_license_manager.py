"""
离线许可证管理器
专为无网络环境设计的安全许可证验证系统
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from .crypto_utils import crypto_manager
from .offline_time_validator import offline_time_validator, OfflineTimeValidationResult

logger = logging.getLogger(__name__)

class OfflineLicenseStatus(Enum):
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    NOT_FOUND = "not_found"
    CORRUPTED = "corrupted"
    TIME_TAMPERED = "time_tampered"
    SECURITY_RISK = "security_risk"  # 新增：安全风险状态

@dataclass
class OfflineLicenseInfo:
    """离线许可证信息结构"""
    organization: str
    user_id: str
    modules: List[str]
    issue_date: str
    start_date: str
    end_date: str
    max_executions: int
    current_executions: int
    license_id: str
    signature: str
    # 离线安全字段
    offline_mode: bool = True
    security_level: str = "standard"
    last_validation: str = ""
    time_proof_chain: List[str] = None
    
    def __post_init__(self):
        if self.time_proof_chain is None:
            self.time_proof_chain = []

class OfflineLicenseManager:
    """离线环境许可证管理器"""
    
    def __init__(self, license_file: str = None, strict_mode: bool = True):
        self.license_file = license_file or self._get_default_license_path()
        self.strict_mode = strict_mode  # 严格模式下安全要求更高
        self._license_info: Optional[OfflineLicenseInfo] = None
        self._license_status: OfflineLicenseStatus = OfflineLicenseStatus.NOT_FOUND
        self._time_validation_result: Optional[OfflineTimeValidationResult] = None
        
        # 安全配置
        self.min_security_score = 70 if strict_mode else 50
        self.require_high_confidence = strict_mode
        
    def _get_default_license_path(self) -> str:
        """获取默认许可证文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "app.license")
    
    def load_license(self) -> Tuple[OfflineLicenseStatus, Optional[OfflineLicenseInfo]]:
        """
        加载并验证许可证（离线模式）
        """
        if not os.path.exists(self.license_file):
            self._license_status = OfflineLicenseStatus.NOT_FOUND
            return self._license_status, None
        
        try:
            # 读取加密的许可证文件
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
            
            # 解密许可证数据
            license_data = crypto_manager.decrypt_data(encrypted_data)
            
            # 验证签名
            signature = license_data.pop('signature', '')
            if not crypto_manager.verify_signature(license_data, signature):
                self._license_status = OfflineLicenseStatus.CORRUPTED
                return self._license_status, None
            
            # 重新添加签名到数据中
            license_data['signature'] = signature
            
            # 兼容旧版许可证格式
            self._upgrade_license_format(license_data)
            
            # 创建许可证信息对象
            self._license_info = OfflineLicenseInfo(**license_data)
            
            # 执行离线许可证验证
            self._license_status = self._validate_license_offline(self._license_info)
            
            return self._license_status, self._license_info
            
        except Exception as e:
            logger.error(f"许可证加载失败: {e}")
            self._license_status = OfflineLicenseStatus.CORRUPTED
            return self._license_status, None
    
    def _upgrade_license_format(self, license_data: Dict):
        """升级许可证格式以支持离线功能"""
        if 'offline_mode' not in license_data:
            license_data['offline_mode'] = True
        if 'security_level' not in license_data:
            license_data['security_level'] = "standard"
        if 'last_validation' not in license_data:
            license_data['last_validation'] = ""
        if 'time_proof_chain' not in license_data:
            license_data['time_proof_chain'] = []
    
    def _validate_license_offline(self, license_info: OfflineLicenseInfo) -> OfflineLicenseStatus:
        """离线环境下的许可证验证"""
        
        # 1. 执行离线时间验证
        time_result = offline_time_validator.validate_license_time(
            license_info.start_date, 
            license_info.end_date
        )
        self._time_validation_result = time_result
        
        # 2. 检查时间验证结果
        if not time_result.is_valid:
            logger.warning(f"时间验证失败: {time_result.warnings}")
            return OfflineLicenseStatus.EXPIRED
        
        # 3. 安全评分检查
        if time_result.security_score < self.min_security_score:
            logger.warning(f"安全评分不足: {time_result.security_score}/{100}")
            if self.strict_mode:
                return OfflineLicenseStatus.SECURITY_RISK
        
        # 4. 置信度检查
        if self.require_high_confidence and time_result.confidence_level == "low":
            logger.warning("时间验证置信度过低")
            return OfflineLicenseStatus.SECURITY_RISK
        
        # 5. 检查执行次数限制
        if (license_info.max_executions > 0 and 
            license_info.current_executions >= license_info.max_executions):
            return OfflineLicenseStatus.EXPIRED
        
        # 6. 更新最后验证时间
        license_info.last_validation = time_result.current_time.isoformat()
        
        return OfflineLicenseStatus.VALID
    
    def increment_execution_count(self) -> bool:
        """增加执行计数（离线版）"""
        if (self._license_status == OfflineLicenseStatus.VALID and 
            self._license_info and 
            (self._license_info.max_executions == -1 or 
             self._license_info.current_executions < self._license_info.max_executions)):
            
            # 更新执行次数
            self._license_info.current_executions += 1
            
            # 添加时间证明到链中
            current_time = datetime.now()
            time_proof = self._create_execution_proof(current_time)
            self._license_info.time_proof_chain.append(time_proof)
            
            # 保持证明链长度合理
            if len(self._license_info.time_proof_chain) > 50:
                self._license_info.time_proof_chain = self._license_info.time_proof_chain[-25:]
            
            # 保存更新后的许可证
            self._save_updated_license()
            return True
        return False
    
    def _create_execution_proof(self, execution_time: datetime) -> str:
        """创建执行证明"""
        proof_data = {
            'execution_time': execution_time.isoformat(),
            'execution_count': self._license_info.current_executions + 1,
            'license_id': self._license_info.license_id,
            'system_state': self._get_system_state_summary()
        }
        
        # 生成证明签名
        proof_json = json.dumps(proof_data, sort_keys=True)
        proof_signature = crypto_manager.generate_signature(proof_data)
        
        proof_data['signature'] = proof_signature
        return json.dumps(proof_data, separators=(',', ':'))
    
    def _get_system_state_summary(self) -> str:
        """获取系统状态摘要"""
        try:
            import psutil
            state = {
                'boot_time': int(psutil.boot_time()),
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total
            }
            return str(hash(str(state)))
        except:
            return "unknown"
    
    def _save_updated_license(self):
        """保存更新后的许可证（离线版）"""
        if not self._license_info:
            return
        
        try:
            # 准备数据
            license_data = asdict(self._license_info)
            signature = license_data.pop('signature')
            
            # 重新生成签名
            new_signature = crypto_manager.generate_signature(license_data)
            license_data['signature'] = new_signature
            
            # 加密并保存
            encrypted_data = crypto_manager.encrypt_data(license_data)
            with open(self.license_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            # 更新内存中的签名
            self._license_info.signature = new_signature
            
            logger.info("离线许可证更新成功")
            
        except Exception as e:
            logger.error(f"保存许可证失败: {e}")
    
    def get_security_report(self) -> Dict[str, any]:
        """获取详细的安全报告"""
        if not self._time_validation_result:
            return {"status": "unknown", "message": "未进行时间验证"}
        
        report = {
            "license_status": self._license_status.value,
            "offline_mode": True,
            "security_score": self._time_validation_result.security_score,
            "confidence_level": self._time_validation_result.confidence_level,
            "current_time": self._time_validation_result.current_time.isoformat(),
            "warnings": self._time_validation_result.warnings,
            "evidence": self._time_validation_result.evidence,
            "security_recommendations": self._get_security_recommendations(),
            "risk_assessment": self._assess_security_risks()
        }
        
        return report
    
    def _get_security_recommendations(self) -> List[str]:
        """获取安全建议"""
        recommendations = []
        
        if not self._time_validation_result:
            return recommendations
        
        score = self._time_validation_result.security_score
        
        if score < 50:
            recommendations.append("安全评分过低，建议检查系统完整性")
            recommendations.append("确保系统时间设置正确")
            recommendations.append("检查是否存在恶意软件")
        elif score < 70:
            recommendations.append("安全评分中等，建议定期检查系统状态")
            recommendations.append("保持系统更新")
        
        if self._time_validation_result.confidence_level == "low":
            recommendations.append("时间验证置信度低，建议重启系统")
            recommendations.append("检查硬件时钟设置")
        
        if any("异常" in warning for warning in self._time_validation_result.warnings):
            recommendations.append("检测到时间异常，请勿手动修改系统时间")
            recommendations.append("如需调整时间，请联系管理员")
        
        return recommendations
    
    def _assess_security_risks(self) -> Dict[str, str]:
        """评估安全风险"""
        risks = {}
        
        if not self._time_validation_result:
            return risks
        
        score = self._time_validation_result.security_score
        
        if score < 30:
            risks["overall"] = "高风险"
            risks["time_tampering"] = "很可能"
            risks["system_compromise"] = "可能"
        elif score < 60:
            risks["overall"] = "中等风险"
            risks["time_tampering"] = "可能"
            risks["system_compromise"] = "不太可能"
        else:
            risks["overall"] = "低风险"
            risks["time_tampering"] = "不太可能"
            risks["system_compromise"] = "很不可能"
        
        return risks
    
    def get_status_message(self) -> str:
        """获取状态消息（离线版）"""
        if self._license_status == OfflineLicenseStatus.VALID:
            if self._license_info and self._time_validation_result:
                end_date = datetime.fromisoformat(self._license_info.end_date)
                days_left = (end_date - self._time_validation_result.current_time).days
                exec_info = ""
                if self._license_info.max_executions > 0:
                    remaining = self._license_info.max_executions - self._license_info.current_executions
                    exec_info = f", 剩余执行次数: {remaining}"
                
                security_info = f"安全评分: {self._time_validation_result.security_score}/100"
                
                return (f"许可证有效 (离线模式) - 授权给: {self._license_info.organization}\n"
                       f"到期时间: {self._license_info.end_date} ({days_left}天后){exec_info}\n"
                       f"授权模块: {', '.join(self._license_info.modules)}\n"
                       f"{security_info}")
        
        elif self._license_status == OfflineLicenseStatus.SECURITY_RISK:
            return "检测到安全风险，许可证验证受限。请检查系统完整性并联系管理员。"
        
        elif self._license_status == OfflineLicenseStatus.TIME_TAMPERED:
            return "检测到时间篡改，许可证验证失败。请确保系统时间正确并联系管理员。"
        
        elif self._license_status == OfflineLicenseStatus.EXPIRED:
            return "许可证已过期，请联系管理员更新许可证"
        
        elif self._license_status == OfflineLicenseStatus.NOT_FOUND:
            return "未找到许可证文件，请联系管理员获取许可证"
        
        elif self._license_status == OfflineLicenseStatus.CORRUPTED:
            return "许可证文件损坏或被篡改，请联系管理员重新获取许可证"
        
        else:
            return "许可证无效，请联系管理员"
    
    # 保持向后兼容的方法
    def get_authorized_modules(self) -> List[str]:
        """获取授权的模块列表"""
        if self._license_status == OfflineLicenseStatus.VALID and self._license_info:
            return self._license_info.modules.copy()
        return []
    
    def is_module_authorized(self, module_name: str) -> bool:
        """检查模块是否被授权"""
        return module_name in self.get_authorized_modules()
    
    def get_license_info(self) -> Optional[OfflineLicenseInfo]:
        """获取许可证信息"""
        return self._license_info
    
    def get_license_status(self) -> OfflineLicenseStatus:
        """获取许可证状态"""
        return self._license_status

# 全局离线许可证管理器实例
offline_license_manager = OfflineLicenseManager()

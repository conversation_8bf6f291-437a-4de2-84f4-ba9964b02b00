"""
离线时间验证器
专为无网络环境设计的安全时间验证系统
"""

import time
import hashlib
import hmac
import json
import os
import platform
import subprocess
import struct
import psutil
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, List, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class OfflineTimeValidationResult:
    """离线时间验证结果"""
    def __init__(self, is_valid: bool, current_time: datetime, 
                 confidence_level: str, security_score: int,
                 warnings: List[str] = None, evidence: Dict[str, Any] = None):
        self.is_valid = is_valid
        self.current_time = current_time
        self.confidence_level = confidence_level  # 'high', 'medium', 'low'
        self.security_score = security_score  # 0-100
        self.warnings = warnings or []
        self.evidence = evidence or {}  # 验证证据

class OfflineTimeValidator:
    """离线环境安全时间验证器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 离线验证配置
        self.time_anchor_file = self.config_dir / ".time_anchors"
        self.time_proof_chain = self.config_dir / ".time_proof_chain"
        self.system_fingerprint_file = self.config_dir / ".system_fingerprint"
        
        # 安全参数
        self.max_time_jump = 3600  # 1小时最大时间跳跃
        self.min_security_score = 60  # 最低安全评分
        
        # 初始化时间锚点
        self._initialize_time_anchors()
    
    def validate_license_time(self, start_date: str, end_date: str) -> OfflineTimeValidationResult:
        """
        离线环境下验证许可证时间有效性
        """
        warnings = []
        evidence = {}
        security_score = 0
        
        # 1. 获取多源时间信息
        time_sources = self._get_multiple_time_sources()
        evidence['time_sources'] = time_sources
        
        # 2. 选择最可信的时间
        trusted_time, time_confidence = self._select_trusted_time(time_sources)
        evidence['trusted_time'] = trusted_time.isoformat()
        evidence['time_confidence'] = time_confidence
        
        # 3. 检测时间异常
        anomaly_score = self._detect_time_anomalies(trusted_time, time_sources)
        evidence['anomaly_score'] = anomaly_score
        security_score += max(0, 30 - anomaly_score)  # 异常越少分数越高
        
        # 4. 验证时间锚点
        anchor_score = self._verify_time_anchors(trusted_time)
        evidence['anchor_score'] = anchor_score
        security_score += anchor_score
        
        # 5. 检查系统完整性
        integrity_score = self._check_system_integrity()
        evidence['integrity_score'] = integrity_score
        security_score += integrity_score
        
        # 6. 验证时间证明链
        proof_score = self._verify_time_proof_chain(trusted_time)
        evidence['proof_score'] = proof_score
        security_score += proof_score
        
        # 7. 执行时间范围检查
        try:
            start_dt = datetime.fromisoformat(start_date)
            end_dt = datetime.fromisoformat(end_date)
        except ValueError as e:
            return OfflineTimeValidationResult(
                False, trusted_time, "low", 0,
                [f"日期格式错误: {e}"], evidence
            )
        
        is_valid = start_dt <= trusted_time <= end_dt
        
        # 8. 生成警告信息
        if security_score < self.min_security_score:
            warnings.append(f"安全评分过低: {security_score}/{100}")
        
        if anomaly_score > 20:
            warnings.append("检测到时间异常，可能存在篡改")
        
        if not is_valid:
            if trusted_time < start_dt:
                warnings.append(f"许可证尚未生效，生效时间: {start_date}")
            elif trusted_time > end_dt:
                warnings.append(f"许可证已过期，过期时间: {end_date}")
        
        # 9. 确定置信度
        if security_score >= 80:
            confidence = "high"
        elif security_score >= 60:
            confidence = "medium"
        else:
            confidence = "low"
        
        return OfflineTimeValidationResult(
            is_valid, trusted_time, confidence, security_score, warnings, evidence
        )
    
    def _get_multiple_time_sources(self) -> Dict[str, datetime]:
        """获取多个本地时间源"""
        sources = {}
        
        # 1. 系统时间
        sources['system'] = datetime.now()
        
        # 2. 硬件时钟时间 (BIOS/UEFI)
        try:
            hw_time = self._get_hardware_clock()
            if hw_time:
                sources['hardware'] = hw_time
        except Exception as e:
            logger.debug(f"无法读取硬件时钟: {e}")
        
        # 3. 文件系统时间
        try:
            fs_time = self._get_filesystem_time()
            if fs_time:
                sources['filesystem'] = fs_time
        except Exception as e:
            logger.debug(f"无法读取文件系统时间: {e}")
        
        # 4. 进程启动时间推算
        try:
            process_time = self._get_process_time()
            if process_time:
                sources['process'] = process_time
        except Exception as e:
            logger.debug(f"无法计算进程时间: {e}")
        
        return sources
    
    def _get_hardware_clock(self) -> Optional[datetime]:
        """读取硬件时钟时间"""
        try:
            if platform.system() == "Windows":
                # Windows: 使用 wmic 命令
                result = subprocess.run(
                    ['wmic', 'bios', 'get', 'releasedate', '/value'],
                    capture_output=True, text=True, timeout=5
                )
                # 这里简化处理，实际可以读取更精确的硬件时间
                return None  # Windows硬件时钟读取较复杂
            
            elif platform.system() == "Linux":
                # Linux: 读取 /sys/class/rtc/rtc0/time
                rtc_path = Path("/sys/class/rtc/rtc0/time")
                if rtc_path.exists():
                    with open(rtc_path, 'r') as f:
                        rtc_time = f.read().strip()
                    # 解析RTC时间格式
                    return datetime.fromtimestamp(int(rtc_time))
            
            elif platform.system() == "Darwin":
                # macOS: 使用 hwclock 或类似命令
                result = subprocess.run(
                    ['date'], capture_output=True, text=True, timeout=5
                )
                # 简化处理
                return None
                
        except Exception as e:
            logger.debug(f"硬件时钟读取失败: {e}")
        
        return None
    
    def _get_filesystem_time(self) -> Optional[datetime]:
        """从文件系统获取时间参考"""
        try:
            # 使用系统关键文件的时间戳作为参考
            reference_files = [
                "/etc/passwd",  # Linux
                "/System/Library/CoreServices/SystemVersion.plist",  # macOS
                "C:\\Windows\\System32\\kernel32.dll",  # Windows
            ]
            
            for file_path in reference_files:
                if os.path.exists(file_path):
                    stat = os.stat(file_path)
                    # 使用修改时间作为参考
                    return datetime.fromtimestamp(stat.st_mtime)
            
            # 如果没有找到系统文件，使用当前目录
            current_dir = Path(".")
            if current_dir.exists():
                stat = current_dir.stat()
                return datetime.fromtimestamp(stat.st_mtime)
                
        except Exception as e:
            logger.debug(f"文件系统时间获取失败: {e}")
        
        return None
    
    def _get_process_time(self) -> Optional[datetime]:
        """通过进程运行时间推算当前时间"""
        try:
            # 获取当前进程信息
            current_process = psutil.Process()
            create_time = current_process.create_time()
            
            # 计算进程运行时间
            process_start = datetime.fromtimestamp(create_time)
            
            # 这里可以结合其他信息来推算更准确的时间
            # 简化处理：假设进程刚启动
            return process_start + timedelta(seconds=time.time() - create_time)
            
        except Exception as e:
            logger.debug(f"进程时间计算失败: {e}")
        
        return None
    
    def _select_trusted_time(self, time_sources: Dict[str, datetime]) -> Tuple[datetime, str]:
        """从多个时间源中选择最可信的时间"""
        if not time_sources:
            return datetime.now(), "low"
        
        # 如果只有一个时间源
        if len(time_sources) == 1:
            return list(time_sources.values())[0], "low"
        
        # 多个时间源的情况，计算中位数或平均值
        times = list(time_sources.values())
        timestamps = [t.timestamp() for t in times]
        
        # 计算时间差异
        max_diff = max(timestamps) - min(timestamps)
        
        if max_diff <= 60:  # 1分钟内差异
            # 使用平均时间
            avg_timestamp = sum(timestamps) / len(timestamps)
            return datetime.fromtimestamp(avg_timestamp), "high"
        elif max_diff <= 300:  # 5分钟内差异
            # 使用中位数
            sorted_timestamps = sorted(timestamps)
            median_timestamp = sorted_timestamps[len(sorted_timestamps) // 2]
            return datetime.fromtimestamp(median_timestamp), "medium"
        else:
            # 差异过大，使用系统时间但降低可信度
            return time_sources.get('system', datetime.now()), "low"
    
    def _detect_time_anomalies(self, current_time: datetime, 
                             time_sources: Dict[str, datetime]) -> int:
        """检测时间异常，返回异常评分 (0-100，越高越异常)"""
        anomaly_score = 0
        
        # 1. 检查时间源之间的差异
        if len(time_sources) > 1:
            times = list(time_sources.values())
            timestamps = [t.timestamp() for t in times]
            max_diff = max(timestamps) - min(timestamps)
            
            if max_diff > 3600:  # 1小时
                anomaly_score += 40
            elif max_diff > 300:  # 5分钟
                anomaly_score += 20
            elif max_diff > 60:  # 1分钟
                anomaly_score += 10
        
        # 2. 检查时间跳跃
        last_time = self._get_last_recorded_time()
        if last_time:
            time_diff = abs((current_time - last_time).total_seconds())
            expected_diff = time.time() - self._get_last_record_timestamp()
            
            if abs(time_diff - expected_diff) > self.max_time_jump:
                anomaly_score += 30
        
        # 3. 检查系统启动时间一致性
        try:
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime = (current_time - boot_time).total_seconds()
            
            # 检查系统运行时间是否合理
            if uptime < 0 or uptime > 365 * 24 * 3600:  # 负数或超过1年
                anomaly_score += 20
        except:
            pass
        
        return min(anomaly_score, 100)
    
    def _initialize_time_anchors(self):
        """初始化时间锚点"""
        if not self.time_anchor_file.exists():
            anchors = {
                'creation_time': datetime.now().isoformat(),
                'system_info': self._get_system_fingerprint(),
                'anchor_points': []
            }
            self._save_secure_data(self.time_anchor_file, anchors)
    
    def _verify_time_anchors(self, current_time: datetime) -> int:
        """验证时间锚点，返回可信度评分 (0-30)"""
        try:
            anchors = self._load_secure_data(self.time_anchor_file)
            if not anchors:
                return 0
            
            creation_time = datetime.fromisoformat(anchors['creation_time'])
            
            # 检查时间单调性
            if current_time < creation_time:
                logger.warning("检测到时间回滚到锚点创建之前")
                return 0
            
            # 检查时间合理性
            time_diff = (current_time - creation_time).total_seconds()
            if time_diff > 365 * 24 * 3600:  # 超过1年
                logger.warning("时间锚点过于久远")
                return 10
            
            return 30
            
        except Exception as e:
            logger.error(f"时间锚点验证失败: {e}")
            return 0

    def _check_system_integrity(self) -> int:
        """检查系统完整性，返回评分 (0-20)"""
        try:
            current_fingerprint = self._get_system_fingerprint()
            stored_fingerprint = self._load_secure_data(self.system_fingerprint_file)

            if not stored_fingerprint:
                # 首次运行，保存指纹
                self._save_secure_data(self.system_fingerprint_file, current_fingerprint)
                return 15

            # 比较系统指纹
            similarity = self._compare_fingerprints(current_fingerprint, stored_fingerprint)

            if similarity > 0.9:
                return 20
            elif similarity > 0.7:
                return 15
            elif similarity > 0.5:
                return 10
            else:
                logger.warning("系统环境发生重大变化")
                return 0

        except Exception as e:
            logger.error(f"系统完整性检查失败: {e}")
            return 0

    def _verify_time_proof_chain(self, current_time: datetime) -> int:
        """验证时间证明链，返回评分 (0-20)"""
        try:
            proof_chain = self._load_secure_data(self.time_proof_chain)

            if not proof_chain:
                # 创建新的证明链
                new_proof = self._create_time_proof(current_time)
                self._save_secure_data(self.time_proof_chain, [new_proof])
                return 10

            # 验证证明链的完整性
            if self._validate_proof_chain(proof_chain, current_time):
                # 添加新的证明到链中
                new_proof = self._create_time_proof(current_time)
                proof_chain.append(new_proof)

                # 保持链的长度在合理范围内
                if len(proof_chain) > 100:
                    proof_chain = proof_chain[-50:]  # 保留最近50个证明

                self._save_secure_data(self.time_proof_chain, proof_chain)
                return 20
            else:
                logger.warning("时间证明链验证失败")
                return 0

        except Exception as e:
            logger.error(f"时间证明链验证失败: {e}")
            return 0

    def _get_system_fingerprint(self) -> Dict[str, Any]:
        """获取系统指纹"""
        fingerprint = {}

        try:
            # 基本系统信息
            fingerprint['platform'] = platform.platform()
            fingerprint['processor'] = platform.processor()
            fingerprint['machine'] = platform.machine()

            # CPU信息
            fingerprint['cpu_count'] = psutil.cpu_count()
            fingerprint['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None

            # 内存信息
            memory = psutil.virtual_memory()
            fingerprint['memory_total'] = memory.total

            # 磁盘信息
            disk_usage = psutil.disk_usage('/')
            fingerprint['disk_total'] = disk_usage.total

            # 网络接口（不包含IP，只包含MAC地址）
            network_interfaces = []
            for interface, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if addr.family == psutil.AF_LINK:  # MAC地址
                        network_interfaces.append({
                            'interface': interface,
                            'mac': addr.address
                        })
            fingerprint['network_interfaces'] = network_interfaces

        except Exception as e:
            logger.debug(f"获取系统指纹部分失败: {e}")

        return fingerprint

    def _compare_fingerprints(self, fp1: Dict[str, Any], fp2: Dict[str, Any]) -> float:
        """比较两个系统指纹的相似度"""
        if not fp1 or not fp2:
            return 0.0

        total_score = 0
        max_score = 0

        # 比较各个字段
        fields_weights = {
            'platform': 0.2,
            'processor': 0.2,
            'machine': 0.1,
            'cpu_count': 0.1,
            'memory_total': 0.1,
            'disk_total': 0.1,
            'network_interfaces': 0.2
        }

        for field, weight in fields_weights.items():
            max_score += weight

            if field in fp1 and field in fp2:
                if field == 'network_interfaces':
                    # 特殊处理网络接口
                    if self._compare_network_interfaces(fp1[field], fp2[field]):
                        total_score += weight
                elif fp1[field] == fp2[field]:
                    total_score += weight
                elif field in ['memory_total', 'disk_total']:
                    # 内存和磁盘允许一定差异
                    if abs(fp1[field] - fp2[field]) / max(fp1[field], fp2[field]) < 0.1:
                        total_score += weight * 0.8

        return total_score / max_score if max_score > 0 else 0.0

    def _compare_network_interfaces(self, interfaces1: List[Dict], interfaces2: List[Dict]) -> bool:
        """比较网络接口"""
        if not interfaces1 or not interfaces2:
            return False

        macs1 = {iface['mac'] for iface in interfaces1}
        macs2 = {iface['mac'] for iface in interfaces2}

        # 至少有一个MAC地址匹配
        return len(macs1.intersection(macs2)) > 0

    def _create_time_proof(self, current_time: datetime) -> Dict[str, Any]:
        """创建时间证明"""
        proof = {
            'timestamp': current_time.isoformat(),
            'system_uptime': time.time() - psutil.boot_time(),
            'process_id': os.getpid(),
            'random_nonce': os.urandom(16).hex(),
            'system_hash': self._get_system_state_hash()
        }

        # 生成证明签名
        proof_data = json.dumps(proof, sort_keys=True)
        signature = hmac.new(
            self._get_signing_key(),
            proof_data.encode(),
            hashlib.sha256
        ).hexdigest()

        proof['signature'] = signature
        return proof

    def _validate_proof_chain(self, proof_chain: List[Dict], current_time: datetime) -> bool:
        """验证时间证明链"""
        if not proof_chain:
            return True

        # 验证每个证明的签名
        for proof in proof_chain:
            if not self._verify_proof_signature(proof):
                return False

        # 验证时间单调性
        for i in range(1, len(proof_chain)):
            prev_time = datetime.fromisoformat(proof_chain[i-1]['timestamp'])
            curr_time = datetime.fromisoformat(proof_chain[i]['timestamp'])

            if curr_time < prev_time:
                logger.warning("检测到时间证明链中的时间回滚")
                return False

        # 验证与当前时间的一致性
        if proof_chain:
            last_proof_time = datetime.fromisoformat(proof_chain[-1]['timestamp'])
            if current_time < last_proof_time:
                logger.warning("当前时间早于最后一个时间证明")
                return False

        return True

    def _verify_proof_signature(self, proof: Dict[str, Any]) -> bool:
        """验证时间证明签名"""
        try:
            signature = proof.pop('signature')
            proof_data = json.dumps(proof, sort_keys=True)

            expected_signature = hmac.new(
                self._get_signing_key(),
                proof_data.encode(),
                hashlib.sha256
            ).hexdigest()

            proof['signature'] = signature  # 恢复签名

            return hmac.compare_digest(signature, expected_signature)

        except Exception as e:
            logger.error(f"时间证明签名验证失败: {e}")
            return False

    def _get_system_state_hash(self) -> str:
        """获取系统状态哈希"""
        try:
            # 收集系统状态信息
            state_info = {
                'boot_time': psutil.boot_time(),
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'process_count': len(psutil.pids())
            }

            state_data = json.dumps(state_info, sort_keys=True)
            return hashlib.sha256(state_data.encode()).hexdigest()[:16]

        except Exception:
            return "unknown"

    def _get_signing_key(self) -> bytes:
        """获取签名密钥"""
        # 基于系统特征生成一致的密钥
        fingerprint = self._get_system_fingerprint()
        key_data = json.dumps(fingerprint, sort_keys=True)
        return hashlib.sha256(key_data.encode()).digest()

    def _save_secure_data(self, file_path: Path, data: Any):
        """安全保存数据"""
        try:
            json_data = json.dumps(data, ensure_ascii=False, indent=2)

            # 生成数据哈希
            data_hash = hashlib.sha256(json_data.encode()).hexdigest()

            # 保存数据和哈希
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(json_data + '\n' + data_hash)

        except Exception as e:
            logger.error(f"保存安全数据失败: {e}")

    def _load_secure_data(self, file_path: Path) -> Any:
        """安全加载数据"""
        try:
            if not file_path.exists():
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            lines = content.split('\n')
            if len(lines) < 2:
                return None

            json_data = '\n'.join(lines[:-1])
            stored_hash = lines[-1]

            # 验证数据完整性
            calculated_hash = hashlib.sha256(json_data.encode()).hexdigest()
            if calculated_hash != stored_hash:
                logger.warning(f"数据完整性验证失败: {file_path}")
                return None

            return json.loads(json_data)

        except Exception as e:
            logger.error(f"加载安全数据失败: {e}")
            return None

    def _get_last_recorded_time(self) -> Optional[datetime]:
        """获取最后记录的时间"""
        try:
            proof_chain = self._load_secure_data(self.time_proof_chain)
            if proof_chain and len(proof_chain) > 0:
                return datetime.fromisoformat(proof_chain[-1]['timestamp'])
        except:
            pass
        return None

    def _get_last_record_timestamp(self) -> float:
        """获取最后记录的时间戳"""
        try:
            if self.time_proof_chain.exists():
                return self.time_proof_chain.stat().st_mtime
        except:
            pass
        return time.time()

# 全局离线时间验证器实例
offline_time_validator = OfflineTimeValidator()

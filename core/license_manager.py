import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

from .crypto_utils import crypto_manager

class LicenseStatus(Enum):
    VALID = "valid"
    EXPIRED = "expired"
    INVALID = "invalid"
    NOT_FOUND = "not_found"
    CORRUPTED = "corrupted"

@dataclass
class LicenseInfo:
    """许可证信息结构"""
    organization: str           # 组织名称
    user_id: str               # 用户标识
    modules: List[str]         # 授权模块列表
    issue_date: str            # 签发日期
    start_date: str            # 生效日期
    end_date: str              # 到期日期
    max_executions: int        # 最大执行次数 (-1表示无限制)
    current_executions: int    # 当前执行次数
    license_id: str            # 许可证唯一标识
    signature: str             # 数字签名

class LicenseManager:
    def __init__(self, license_file: str = None):
        """初始化许可证管理器"""
        self.license_file = license_file or self._get_default_license_path()
        self._license_info: Optional[LicenseInfo] = None
        self._license_status: LicenseStatus = LicenseStatus.NOT_FOUND
    
    def _get_default_license_path(self) -> str:
        """获取默认许可证文件路径"""
        config_dir = Path(__file__).parent.parent / "config"
        return str(config_dir / "app.license")
    
    def load_license(self) -> Tuple[LicenseStatus, Optional[LicenseInfo]]:
        """加载并验证许可证"""
        if not os.path.exists(self.license_file):
            self._license_status = LicenseStatus.NOT_FOUND
            return self._license_status, None
        
        try:
            # 读取加密的许可证文件
            with open(self.license_file, 'r', encoding='utf-8') as f:
                encrypted_data = f.read().strip()
            
            # 解密许可证数据
            license_data = crypto_manager.decrypt_data(encrypted_data)
            
            # 验证签名
            signature = license_data.pop('signature', '')
            if not crypto_manager.verify_signature(license_data, signature):
                self._license_status = LicenseStatus.CORRUPTED
                return self._license_status, None
            
            # 重新添加签名到数据中
            license_data['signature'] = signature
            
            # 创建许可证信息对象
            self._license_info = LicenseInfo(**license_data)
            
            # 验证许可证有效性
            self._license_status = self._validate_license(self._license_info)
            
            return self._license_status, self._license_info
            
        except Exception as e:
            print(f"许可证加载失败: {e}")
            self._license_status = LicenseStatus.CORRUPTED
            return self._license_status, None
    
    def _validate_license(self, license_info: LicenseInfo) -> LicenseStatus:
        """验证许可证有效性"""
        now = datetime.now()
        
        # 检查日期格式和有效性
        try:
            start_date = datetime.fromisoformat(license_info.start_date)
            end_date = datetime.fromisoformat(license_info.end_date)
        except ValueError:
            return LicenseStatus.INVALID
        
        # 检查是否在有效期内
        if now < start_date or now > end_date:
            return LicenseStatus.EXPIRED
        
        # 检查执行次数限制
        if (license_info.max_executions > 0 and 
            license_info.current_executions >= license_info.max_executions):
            return LicenseStatus.EXPIRED
        
        return LicenseStatus.VALID
    
    def get_authorized_modules(self) -> List[str]:
        """获取授权的模块列表"""
        if self._license_status == LicenseStatus.VALID and self._license_info:
            return self._license_info.modules.copy()
        return []
    
    def is_module_authorized(self, module_name: str) -> bool:
        """检查模块是否被授权"""
        return module_name in self.get_authorized_modules()
    
    def get_license_info(self) -> Optional[LicenseInfo]:
        """获取许可证信息"""
        return self._license_info
    
    def get_license_status(self) -> LicenseStatus:
        """获取许可证状态"""
        return self._license_status
    
    def increment_execution_count(self) -> bool:
        """增加执行计数"""
        if (self._license_status == LicenseStatus.VALID and 
            self._license_info and 
            (self._license_info.max_executions == -1 or 
             self._license_info.current_executions < self._license_info.max_executions)):
            
            self._license_info.current_executions += 1
            self._save_updated_license()
            return True
        return False
    
    def _save_updated_license(self):
        """保存更新后的许可证"""
        if not self._license_info:
            return
        
        try:
            # 准备数据
            license_data = asdict(self._license_info)
            signature = license_data.pop('signature')
            
            # 重新生成签名
            new_signature = crypto_manager.generate_signature(license_data)
            license_data['signature'] = new_signature
            
            # 加密并保存
            encrypted_data = crypto_manager.encrypt_data(license_data)
            with open(self.license_file, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)
                
            # 更新内存中的签名
            self._license_info.signature = new_signature
            
        except Exception as e:
            print(f"保存许可证失败: {e}")
    
    def get_status_message(self) -> str:
        """获取状态消息"""
        if self._license_status == LicenseStatus.VALID:
            if self._license_info:
                end_date = datetime.fromisoformat(self._license_info.end_date)
                days_left = (end_date - datetime.now()).days
                exec_info = ""
                if self._license_info.max_executions > 0:
                    remaining = self._license_info.max_executions - self._license_info.current_executions
                    exec_info = f", 剩余执行次数: {remaining}"
                
                return (f"许可证有效 - 授权给: {self._license_info.organization}\n"
                       f"到期时间: {self._license_info.end_date} ({days_left}天后){exec_info}\n"
                       f"授权模块: {', '.join(self._license_info.modules)}")
        
        elif self._license_status == LicenseStatus.EXPIRED:
            return "许可证已过期，请联系管理员更新许可证"
        
        elif self._license_status == LicenseStatus.NOT_FOUND:
            return "未找到许可证文件，请联系管理员获取许可证"
        
        elif self._license_status == LicenseStatus.CORRUPTED:
            return "许可证文件损坏或被篡改，请联系管理员重新获取许可证"
        
        else:
            return "许可证无效，请联系管理员"

# 全局许可证管理器实例
license_manager = LicenseManager()
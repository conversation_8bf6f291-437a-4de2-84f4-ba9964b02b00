"""
安全时间验证器
实现多层时间验证机制，防止时间篡改攻击
"""

import time
import hashlib
import hmac
import json
import requests
from datetime import datetime, timedelta
from typing import Optional, Tuple, Dict, List
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class TimeValidationResult:
    """时间验证结果"""
    def __init__(self, is_valid: bool, current_time: datetime, 
                 confidence_level: str, warnings: List[str] = None):
        self.is_valid = is_valid
        self.current_time = current_time
        self.confidence_level = confidence_level  # 'high', 'medium', 'low'
        self.warnings = warnings or []

class SecureTimeValidator:
    """安全时间验证器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or "config/time_config.json"
        self.time_servers = [
            "time.google.com",
            "time.cloudflare.com", 
            "pool.ntp.org",
            "time.windows.com"
        ]
        self.time_cache_file = "config/.time_cache"
        self.max_time_drift = 300  # 5分钟最大时间偏移
        self.network_timeout = 5   # 网络超时时间
        
    def validate_license_time(self, start_date: str, end_date: str) -> TimeValidationResult:
        """
        验证许可证时间有效性
        使用多层验证机制确保时间准确性
        """
        warnings = []
        
        # 1. 获取可信时间
        trusted_time, confidence = self._get_trusted_time()
        
        # 2. 检测时间篡改
        tampering_detected = self._detect_time_tampering(trusted_time)
        if tampering_detected:
            warnings.append("检测到可能的系统时间篡改")
            
        # 3. 验证时间范围
        try:
            start_dt = datetime.fromisoformat(start_date)
            end_dt = datetime.fromisoformat(end_date)
        except ValueError as e:
            return TimeValidationResult(False, trusted_time, "low", 
                                      [f"日期格式错误: {e}"])
        
        # 4. 执行时间范围检查
        is_valid = start_dt <= trusted_time <= end_dt
        
        # 5. 添加时间边界检查
        if trusted_time < start_dt:
            warnings.append(f"许可证尚未生效，生效时间: {start_date}")
        elif trusted_time > end_dt:
            warnings.append(f"许可证已过期，过期时间: {end_date}")
            
        return TimeValidationResult(is_valid, trusted_time, confidence, warnings)
    
    def _get_trusted_time(self) -> Tuple[datetime, str]:
        """
        获取可信时间源
        优先级：网络时间 > 缓存时间 > 本地时间
        """
        # 1. 尝试获取网络时间
        network_time = self._get_network_time()
        if network_time:
            self._cache_time_reference(network_time)
            return network_time, "high"
        
        # 2. 使用缓存时间推算
        cached_time = self._get_cached_time()
        if cached_time:
            return cached_time, "medium"
        
        # 3. 降级到本地时间（最低可信度）
        logger.warning("无法获取网络时间，使用本地时间（安全性降低）")
        return datetime.now(), "low"
    
    def _get_network_time(self) -> Optional[datetime]:
        """从多个时间服务器获取网络时间"""
        for server in self.time_servers:
            try:
                # 使用HTTP时间API
                if server == "time.google.com":
                    response = requests.get(
                        "https://timeapi.io/api/Time/current/zone?timeZone=UTC",
                        timeout=self.network_timeout
                    )
                    if response.status_code == 200:
                        data = response.json()
                        return datetime.fromisoformat(data['dateTime'].replace('Z', '+00:00'))
                
                elif server == "time.cloudflare.com":
                    response = requests.get(
                        "https://cloudflare-dns.com/dns-query",
                        headers={"Accept": "application/dns-json"},
                        params={"name": "time.cloudflare.com", "type": "A"},
                        timeout=self.network_timeout
                    )
                    # 从响应头获取时间
                    if response.status_code == 200:
                        date_header = response.headers.get('Date')
                        if date_header:
                            return datetime.strptime(date_header, '%a, %d %b %Y %H:%M:%S %Z')
                            
            except Exception as e:
                logger.debug(f"时间服务器 {server} 连接失败: {e}")
                continue
        
        return None
    
    def _cache_time_reference(self, network_time: datetime):
        """缓存时间参考点"""
        try:
            cache_data = {
                'network_time': network_time.isoformat(),
                'local_time': datetime.now().isoformat(),
                'timestamp': time.time()
            }
            
            # 加密缓存数据
            cache_json = json.dumps(cache_data)
            cache_hash = hashlib.sha256(cache_json.encode()).hexdigest()
            
            with open(self.time_cache_file, 'w') as f:
                f.write(f"{cache_json}\n{cache_hash}")
                
        except Exception as e:
            logger.error(f"缓存时间参考失败: {e}")
    
    def _get_cached_time(self) -> Optional[datetime]:
        """从缓存推算当前时间"""
        try:
            if not Path(self.time_cache_file).exists():
                return None
                
            with open(self.time_cache_file, 'r') as f:
                lines = f.read().strip().split('\n')
                if len(lines) != 2:
                    return None
                    
                cache_json, stored_hash = lines
                
            # 验证缓存完整性
            calculated_hash = hashlib.sha256(cache_json.encode()).hexdigest()
            if calculated_hash != stored_hash:
                logger.warning("时间缓存文件被篡改")
                return None
            
            cache_data = json.loads(cache_json)
            
            # 检查缓存年龄
            cache_age = time.time() - cache_data['timestamp']
            if cache_age > 86400:  # 24小时过期
                return None
            
            # 推算当前时间
            cached_network_time = datetime.fromisoformat(cache_data['network_time'])
            cached_local_time = datetime.fromisoformat(cache_data['local_time'])
            current_local_time = datetime.now()
            
            # 计算时间偏移
            local_drift = current_local_time - cached_local_time
            estimated_network_time = cached_network_time + local_drift
            
            return estimated_network_time
            
        except Exception as e:
            logger.error(f"读取时间缓存失败: {e}")
            return None
    
    def _detect_time_tampering(self, trusted_time: datetime) -> bool:
        """检测系统时间篡改"""
        local_time = datetime.now()
        time_diff = abs((trusted_time - local_time).total_seconds())
        
        # 如果时间差异超过阈值，可能存在篡改
        if time_diff > self.max_time_drift:
            logger.warning(f"检测到时间异常：本地时间与可信时间相差 {time_diff} 秒")
            return True
            
        return False
    
    def create_time_proof(self, license_data: Dict) -> str:
        """
        创建时间证明
        用于防止许可证文件回滚攻击
        """
        current_time = datetime.now()
        proof_data = {
            'timestamp': current_time.isoformat(),
            'license_id': license_data.get('license_id'),
            'execution_count': license_data.get('current_executions', 0),
            'proof_id': hashlib.sha256(f"{current_time.isoformat()}{license_data.get('license_id')}".encode()).hexdigest()[:16]
        }
        
        # 生成时间证明签名
        proof_json = json.dumps(proof_data, sort_keys=True)
        proof_signature = hmac.new(
            b"time_proof_key",  # 实际应用中应使用更安全的密钥
            proof_json.encode(),
            hashlib.sha256
        ).hexdigest()
        
        proof_data['signature'] = proof_signature
        return json.dumps(proof_data)
    
    def verify_time_proof(self, current_proof: str, stored_proof: str) -> bool:
        """验证时间证明，防止回滚攻击"""
        try:
            current_data = json.loads(current_proof)
            stored_data = json.loads(stored_proof)
            
            # 验证签名
            for data in [current_data, stored_data]:
                signature = data.pop('signature')
                expected_signature = hmac.new(
                    b"time_proof_key",
                    json.dumps(data, sort_keys=True).encode(),
                    hashlib.sha256
                ).hexdigest()
                
                if signature != expected_signature:
                    return False
                    
                data['signature'] = signature
            
            # 检查时间单调性
            current_time = datetime.fromisoformat(current_data['timestamp'])
            stored_time = datetime.fromisoformat(stored_data['timestamp'])
            
            if current_time < stored_time:
                logger.warning("检测到时间回滚攻击")
                return False
            
            # 检查执行次数单调性
            if current_data['execution_count'] < stored_data['execution_count']:
                logger.warning("检测到执行次数回滚")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"时间证明验证失败: {e}")
            return False

# 全局安全时间验证器实例
secure_time_validator = SecureTimeValidator()

from pydantic import BaseModel, Field

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("警告: PyYAML未安装，无法解析YAML配置文件。请运行: pip install PyYAML")


class ToolConfig(BaseModel):
    """工具配置"""
    ocr_language: str = Field("eng", description="识别语言，默认为英文")  # 识别语言，默认为英文
    qps: int = Field(10, description="翻译请求速度")  # 翻译请求速度
    limits: int = Field(5, description="并发处理数量")  # 并发处理数量

class LLMConfig(BaseModel):
    """LLM配置"""
    base_url: str = Field("https://api.openai.com/v1", description="OpenAI API地址")  # OpenAI API地址
    api_key: str = Field("", description="OpenAI API密钥")
    model: str = Field("gpt-3.5-turbo", description="模型名称")
    translate_prompt: str = Field("", description="翻译提示词，可以为空")
    summarize_prompt: str = Field("", description="摘要提示词，可以为空")
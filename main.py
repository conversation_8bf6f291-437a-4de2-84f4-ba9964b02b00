#!/usr/bin/env python3
"""
现代化CLI应用程序主入口
基于Click框架，支持动态模块注册和许可证验证
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

# 导入所有模块以触发注册
try:
    from modules import data_cleaning, translation, summarization
    from core.cli_framework import cli_app
    from core.exceptions import CLIError
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保安装了所有依赖: pip install -r requirements.txt")
    sys.exit(1)

def main():
    """主程序入口"""
    try:
        # 运行CLI应用
        cli_app.run()
    except CLIError as e:
        print(f"应用程序错误: {e.message}")
        sys.exit(e.error_code)
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(130)
    except Exception as e:
        print(f"未知错误: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
#!/usr/bin/env python3
import argparse
import sys
from pathlib import Path

# 导入所有模块以触发注册
from modules import data_cleaning, translation, summarization
# from core.registry import registry
from core.auth import PermissionManager
from core.license_manager import LicenseStatus


def create_parser():
    """创建命令行解析器"""
    parser = argparse.ArgumentParser(
        description='离线多功能CLI应用程序 (基于许可证)',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # 检查许可证状态
    status, license_info = PermissionManager.check_license_status()

    if status != LicenseStatus.VALID:
        parser.epilog = f"许可证状态: {PermissionManager.get_status_message()}"
        # 如果许可证无效，只显示帮助信息
        return parser

    # 获取授权模块
    authorized_modules = PermissionManager.get_authorized_modules()
    license_info = PermissionManager.get_license_info()

    if license_info:
        parser.epilog = (f"许可证持有者: {license_info.organization}\n"
                         f"授权模块: {', '.join(authorized_modules)}\n"
                         f"到期时间: {license_info.end_date}")

    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 动态添加子命令
    if 'data_cleaning' in authorized_modules:
        clean_parser = subparsers.add_parser('clean', help='数据清理')
        clean_parser.add_argument('input', help='输入文件路径')
        clean_parser.add_argument('-o', '--output', help='输出文件路径')

    if 'translation' in authorized_modules:
        trans_parser = subparsers.add_parser('translate', help='文本翻译')
        trans_parser.add_argument('text', help='要翻译的文本')
        trans_parser.add_argument('--source', default='en', help='源语言')
        trans_parser.add_argument('--target', default='zh', help='目标语言')

    if 'summarization' in authorized_modules:
        summary_parser = subparsers.add_parser('summarize', help='文本摘要')
        summary_parser.add_argument('input', help='输入文件路径')
        summary_parser.add_argument('-l', '--length', type=int, default=3, help='摘要长度')

    # 添加许可证信息命令
    subparsers.add_parser('license-info', help='显示许可证信息')

    return parser


def main():
    """主程序入口"""
    parser = create_parser()
    args = parser.parse_args()

    # 首先检查许可证状态
    status, _ = PermissionManager.check_license_status()

    if status != LicenseStatus.VALID:
        print("错误:", PermissionManager.get_status_message())
        if status == LicenseStatus.NOT_FOUND:
            print("\n请联系管理员获取许可证文件，并将其放置在 config/app.license")
        elif status == LicenseStatus.EXPIRED:
            print("\n请联系管理员更新许可证")
        elif status == LicenseStatus.CORRUPTED:
            print("\n许可证文件可能被篡改，请重新获取有效的许可证文件")
        sys.exit(1)

    if not args.command:
        parser.print_help()
        return

    try:
        # 执行相应的命令
        if args.command == 'clean':
            from modules.data_cleaning import clean_data
            clean_data(args.input, args.output)

        elif args.command == 'translate':
            from modules.translation import translate
            translate()

        elif args.command == 'summarize':
            from modules.summarization import summarize_file
            summarize_file()

        elif args.command == 'license-info':
            print(PermissionManager.get_status_message())

    except PermissionError as e:
        print(f"权限错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"执行错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
#!/usr/bin/env python3
"""
许可证时间安全测试脚本
演示时间攻击场景和安全防护机制
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import patch

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_current_vulnerability():
    """测试当前许可证系统的时间漏洞"""
    print("=== 当前系统时间漏洞测试 ===")
    
    try:
        from core.license_manager import LicenseManager, LicenseStatus
        
        # 创建测试许可证管理器
        license_manager = LicenseManager()
        
        print("1. 正常时间验证测试")
        status, license_info = license_manager.load_license()
        print(f"   许可证状态: {status}")
        
        if status == LicenseStatus.VALID and license_info:
            print(f"   到期时间: {license_info.end_date}")
            
            # 模拟时间篡改攻击
            print("\n2. 模拟时间篡改攻击")
            
            # 计算一个过期的时间点
            end_date = datetime.fromisoformat(license_info.end_date)
            future_time = end_date + timedelta(days=30)  # 过期30天后
            
            print(f"   模拟当前时间: {future_time}")
            print("   正常情况下许可证应该过期...")
            
            # 使用mock模拟系统时间被篡改
            with patch('core.license_manager.datetime') as mock_datetime:
                mock_datetime.now.return_value = future_time
                mock_datetime.fromisoformat = datetime.fromisoformat
                
                # 重新验证许可证
                tampered_status, _ = license_manager.load_license()
                print(f"   篡改后状态: {tampered_status}")
                
                if tampered_status == LicenseStatus.VALID:
                    print("   🚨 安全漏洞：时间篡改攻击成功！")
                else:
                    print("   ✅ 时间篡改攻击被阻止")
            
            # 模拟时间回滚攻击
            print("\n3. 模拟时间回滚攻击")
            past_time = end_date - timedelta(days=30)  # 回滚到30天前
            print(f"   模拟回滚时间: {past_time}")
            
            with patch('core.license_manager.datetime') as mock_datetime:
                mock_datetime.now.return_value = past_time
                mock_datetime.fromisoformat = datetime.fromisoformat
                
                rollback_status, _ = license_manager.load_license()
                print(f"   回滚后状态: {rollback_status}")
                
                if rollback_status == LicenseStatus.VALID:
                    print("   🚨 安全漏洞：时间回滚攻击成功！")
                else:
                    print("   ✅ 时间回滚攻击被阻止")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_enhanced_security():
    """测试增强的安全时间验证"""
    print("\n=== 增强安全系统测试 ===")
    
    try:
        from core.enhanced_license_manager import EnhancedLicenseManager, LicenseStatus
        from core.secure_time_validator import secure_time_validator
        
        # 创建增强许可证管理器
        enhanced_manager = EnhancedLicenseManager()
        
        print("1. 增强时间验证测试")
        status, license_info = enhanced_manager.load_license(strict_time_validation=True)
        print(f"   许可证状态: {status}")
        
        # 获取安全状态报告
        security_status = enhanced_manager.get_security_status()
        print(f"   时间可信度: {security_status.get('time_confidence', 'unknown')}")
        
        if security_status.get('warnings'):
            print("   安全警告:")
            for warning in security_status['warnings']:
                print(f"     - {warning}")
        
        if security_status.get('security_recommendations'):
            print("   安全建议:")
            for rec in security_status['security_recommendations']:
                print(f"     - {rec}")
        
        print("\n2. 时间证明机制测试")
        if license_info:
            # 创建时间证明
            license_dict = {
                'license_id': license_info.license_id,
                'current_executions': license_info.current_executions
            }
            
            proof1 = secure_time_validator.create_time_proof(license_dict)
            print("   ✅ 时间证明创建成功")
            
            # 模拟正常的时间证明验证
            time.sleep(1)  # 等待1秒
            license_dict['current_executions'] += 1
            proof2 = secure_time_validator.create_time_proof(license_dict)
            
            is_valid = secure_time_validator.verify_time_proof(proof2, proof1)
            print(f"   时间证明验证: {'✅ 通过' if is_valid else '❌ 失败'}")
            
            # 模拟回滚攻击
            print("\n3. 时间证明防回滚测试")
            license_dict['current_executions'] -= 1  # 模拟回滚
            proof3 = secure_time_validator.create_time_proof(license_dict)
            
            is_rollback_detected = not secure_time_validator.verify_time_proof(proof3, proof2)
            print(f"   回滚检测: {'✅ 检测到' if is_rollback_detected else '❌ 未检测到'}")
        
        return True
        
    except ImportError as e:
        print(f"   ⚠️  增强安全模块未安装: {e}")
        print("   提示: 需要安装 requests 库来支持网络时间验证")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_network_time_validation():
    """测试网络时间验证"""
    print("\n=== 网络时间验证测试 ===")
    
    try:
        from core.secure_time_validator import secure_time_validator
        
        print("1. 网络时间获取测试")
        network_time = secure_time_validator._get_network_time()
        
        if network_time:
            print(f"   ✅ 网络时间获取成功: {network_time}")
            
            local_time = datetime.now()
            time_diff = abs((network_time - local_time).total_seconds())
            print(f"   本地时间: {local_time}")
            print(f"   时间差异: {time_diff:.2f} 秒")
            
            if time_diff > 300:  # 5分钟
                print("   ⚠️  时间差异较大，可能存在时间同步问题")
            else:
                print("   ✅ 时间同步正常")
        else:
            print("   ⚠️  无法获取网络时间（可能是网络问题）")
        
        print("\n2. 时间缓存机制测试")
        if network_time:
            secure_time_validator._cache_time_reference(network_time)
            print("   ✅ 时间缓存创建成功")
            
            cached_time = secure_time_validator._get_cached_time()
            if cached_time:
                print(f"   ✅ 时间缓存读取成功: {cached_time}")
                cache_diff = abs((cached_time - network_time).total_seconds())
                print(f"   缓存精度: {cache_diff:.2f} 秒")
            else:
                print("   ❌ 时间缓存读取失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def demonstrate_attack_scenarios():
    """演示攻击场景"""
    print("\n=== 攻击场景演示 ===")
    
    scenarios = [
        {
            "name": "场景1: 系统时间回滚攻击",
            "description": "攻击者将系统时间调回到许可证有效期内",
            "impact": "绕过过期检查，延长许可证使用期",
            "detection": "通过网络时间对比检测"
        },
        {
            "name": "场景2: 许可证文件回滚攻击", 
            "description": "攻击者恢复旧的许可证文件以重置执行次数",
            "impact": "重置使用次数限制",
            "detection": "通过时间证明机制检测"
        },
        {
            "name": "场景3: 网络时间欺骗攻击",
            "description": "攻击者在网络层面伪造时间服务器响应",
            "impact": "欺骗网络时间验证",
            "detection": "多时间源验证和时间一致性检查"
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['name']}")
        print(f"   攻击方式: {scenario['description']}")
        print(f"   安全影响: {scenario['impact']}")
        print(f"   检测方法: {scenario['detection']}")

def show_security_recommendations():
    """显示安全建议"""
    print("\n=== 安全加固建议 ===")
    
    recommendations = [
        {
            "level": "基础",
            "items": [
                "启用网络时间同步 (NTP)",
                "定期检查系统时间准确性",
                "监控许可证文件完整性",
                "记录许可证使用日志"
            ]
        },
        {
            "level": "进阶", 
            "items": [
                "部署多重时间验证机制",
                "实施时间证明防回滚",
                "使用硬件安全模块 (HSM)",
                "建立时间篡改检测告警"
            ]
        },
        {
            "level": "企业级",
            "items": [
                "部署专用时间服务器",
                "实施区块链时间戳",
                "使用可信执行环境 (TEE)",
                "建立完整的安全审计体系"
            ]
        }
    ]
    
    for rec in recommendations:
        print(f"\n{rec['level']}安全措施:")
        for item in rec['items']:
            print(f"  • {item}")

def main():
    """主测试函数"""
    print("许可证时间安全分析测试")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("当前系统漏洞", test_current_vulnerability),
        ("增强安全系统", test_enhanced_security),
        ("网络时间验证", test_network_time_validation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    # 显示攻击场景和安全建议
    demonstrate_attack_scenarios()
    show_security_recommendations()
    
    print("\n" + "=" * 60)
    print("安全分析完成")

if __name__ == '__main__':
    main()

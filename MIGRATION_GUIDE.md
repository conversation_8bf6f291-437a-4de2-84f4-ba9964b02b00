# CLI框架升级迁移指南

## 概述

本项目已从基于 `argparse` 的传统CLI框架升级到基于 `Click` 的现代CLI框架。本指南详细说明了迁移的原因、变更内容和使用方法。

## 迁移原因

### 选择Click的优势

1. **装饰器友好**: 与现有的 `@require_license` 装饰器完美集成
2. **动态命令支持**: 可以根据许可证动态注册命令
3. **类型提示**: 更好的参数验证和类型检查
4. **国际化支持**: 内置完整的i18n支持
5. **用户体验**: 更丰富的帮助信息和错误提示
6. **可扩展性**: 更容易添加新功能和命令

## 主要变更

### 1. 文件结构变更

```
新增文件:
├── core/registry.py          # 模块注册器
├── core/exceptions.py        # 自定义异常类
├── core/cli_framework.py     # Click CLI框架
├── requirements.txt          # 依赖管理
└── MIGRATION_GUIDE.md        # 本迁移指南

修改文件:
├── main.py                   # 简化的主入口
├── modules/data_cleaning.py  # 升级为Click命令
├── modules/translation.py    # 新实现的翻译模块
└── modules/summarization.py  # 新实现的摘要模块
```

### 2. 命令行接口变更

#### 旧版本 (argparse)
```bash
# 数据清理
python main.py clean input.csv -o output.csv

# 翻译
python main.py translate "hello world" --source en --target zh

# 摘要
python main.py summarize input.txt -l 3
```

#### 新版本 (Click)
```bash
# 数据清理 - 更多选项
python main.py data_cleaning input.csv -o output.csv --remove-duplicates --remove-na

# 翻译 - 支持文件输入
python main.py translation "hello world" --source auto --target zh
python main.py translation -f input.txt -o output.txt

# 摘要 - 多种算法
python main.py summarization input.txt -l 3 --method hybrid
```

### 3. 错误处理改进

#### 旧版本
```python
try:
    # 操作
except Exception as e:
    print(f"错误: {e}")
```

#### 新版本
```python
from core.exceptions import *

@handle_exception
def my_function():
    # 具体的异常类型
    raise UnsupportedFileFormatError(file_path, supported_formats)
```

## 安装和使用

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 基本使用

```bash
# 查看帮助
python main.py --help

# 查看许可证状态
python main.py license-info

# 查看特定命令帮助
python main.py data_cleaning --help
```

### 3. 功能演示

#### 数据清理
```bash
# 基本清理
python main.py data_cleaning data.csv -o cleaned.csv

# 自定义清理选项
python main.py data_cleaning data.csv -o cleaned.csv --keep-duplicates --remove-na
```

#### 文本翻译
```bash
# 直接翻译文本
python main.py translation "Hello World" --target zh

# 翻译文件
python main.py translation -f input.txt -o output.txt --source en --target zh

# 自动检测语言
python main.py translation "你好世界" --source auto --target en
```

#### 文本摘要
```bash
# 基本摘要
python main.py summarization document.txt -l 5

# 使用不同算法
python main.py summarization document.txt -l 3 --method frequency
python main.py summarization document.txt -l 3 --method position
python main.py summarization document.txt -l 3 --method hybrid
```

## 开发者指南

### 1. 添加新模块

```python
# modules/new_module.py
import click
from core.registry import registry
from core.exceptions import *

@click.command()
@click.argument('input_param')
@click.option('--option', help='选项说明')
@handle_exception
def new_function(input_param, option):
    """新功能描述"""
    # 实现逻辑
    pass

# 注册模块
registry.register(
    'new_module',
    '新模块',
    '新模块的描述',
    new_function
)
```

### 2. 自定义异常

```python
# core/exceptions.py
class CustomError(CLIError):
    def __init__(self, details):
        message = f"自定义错误: {details}"
        super().__init__(message, error_code=50)
```

### 3. 国际化支持

```python
# 在 core/cli_framework.py 中添加新语言
MESSAGES = {
    'zh': {'key': '中文消息'},
    'en': {'key': 'English message'},
    'ja': {'key': '日本語メッセージ'},  # 新增日语
}
```

## 兼容性说明

### 保持不变的功能
- 许可证验证机制
- 模块授权系统
- 加密和签名算法
- 核心业务逻辑

### 改进的功能
- 更友好的错误提示
- 更丰富的命令行选项
- 更好的帮助信息
- 更强的类型检查

## 故障排除

### 常见问题

1. **导入错误**
   ```
   解决方案: pip install -r requirements.txt
   ```

2. **许可证问题**
   ```
   解决方案: 确保 config/app.license 文件存在且有效
   ```

3. **模块未注册**
   ```
   解决方案: 检查模块是否正确导入并调用 registry.register()
   ```

### 调试模式

```bash
# 启用详细日志
export PYTHONPATH=.
python -c "import logging; logging.basicConfig(level=logging.DEBUG)" main.py --help
```

## 性能对比

| 指标 | 旧版本 (argparse) | 新版本 (Click) | 改进 |
|------|------------------|----------------|------|
| 启动时间 | ~0.1s | ~0.15s | -50ms |
| 内存使用 | ~15MB | ~18MB | +3MB |
| 帮助生成 | 基础 | 丰富 | +++++ |
| 错误提示 | 简单 | 详细 | +++++ |
| 扩展性 | 中等 | 优秀 | +++++ |

## 后续计划

1. **短期** (1-2周)
   - 添加更多翻译语言支持
   - 优化摘要算法
   - 添加配置文件支持

2. **中期** (1-2月)
   - 实现插件系统
   - 添加Web界面
   - 支持批量处理

3. **长期** (3-6月)
   - 云端许可证验证
   - 机器学习模型集成
   - 分布式处理支持

## 反馈和支持

如果在迁移过程中遇到问题，请：

1. 检查本指南的故障排除部分
2. 查看日志文件获取详细错误信息
3. 联系开发团队获取支持

---

*本指南会随着项目发展持续更新*

{"offline_security": {"enabled": true, "strict_mode": true, "description": "离线环境安全配置"}, "time_validation": {"max_time_jump_seconds": 3600, "min_security_score": 60, "require_high_confidence": false, "time_sources": {"system_time": {"enabled": true, "weight": 0.3, "description": "系统时间"}, "hardware_clock": {"enabled": true, "weight": 0.4, "description": "硬件时钟"}, "filesystem_time": {"enabled": true, "weight": 0.2, "description": "文件系统时间"}, "process_time": {"enabled": true, "weight": 0.1, "description": "进程时间"}}}, "anomaly_detection": {"time_source_max_diff_seconds": 300, "system_uptime_check": true, "boot_time_consistency": true, "time_jump_threshold_seconds": 3600, "anomaly_score_weights": {"time_source_diff": 0.4, "time_jump": 0.3, "system_consistency": 0.3}}, "system_fingerprint": {"enabled": true, "update_interval_hours": 24, "similarity_threshold": 0.8, "components": {"platform_info": {"enabled": true, "weight": 0.2}, "cpu_info": {"enabled": true, "weight": 0.2}, "memory_info": {"enabled": true, "weight": 0.1}, "disk_info": {"enabled": true, "weight": 0.1}, "network_interfaces": {"enabled": true, "weight": 0.2, "include_mac_addresses": true}, "system_files": {"enabled": true, "weight": 0.2, "reference_files": ["/etc/passwd", "/System/Library/CoreServices/SystemVersion.plist", "C:\\Windows\\System32\\kernel32.dll"]}}}, "time_proof_chain": {"enabled": true, "max_chain_length": 100, "cleanup_threshold": 50, "proof_components": {"timestamp": true, "system_uptime": true, "process_id": true, "random_nonce": true, "system_state_hash": true}, "signature_algorithm": "HMAC-SHA256"}, "security_levels": {"high": {"min_score": 80, "description": "高安全等级，适用于关键环境", "requirements": {"multiple_time_sources": true, "system_fingerprint_match": true, "time_proof_chain_valid": true, "low_anomaly_score": true}}, "medium": {"min_score": 60, "description": "中等安全等级，适用于一般商用环境", "requirements": {"basic_time_validation": true, "system_integrity_check": true, "moderate_anomaly_tolerance": true}}, "low": {"min_score": 40, "description": "低安全等级，适用于开发测试环境", "requirements": {"basic_functionality": true}}}, "risk_thresholds": {"time_tampering": {"low_risk": 10, "medium_risk": 30, "high_risk": 50, "critical_risk": 70}, "system_compromise": {"fingerprint_similarity": 0.7, "anomaly_score": 40, "proof_chain_breaks": 3}}, "logging": {"enabled": true, "log_level": "INFO", "log_file": "logs/offline_security.log", "log_rotation": {"max_size_mb": 10, "backup_count": 5}, "events_to_log": {"license_validation": true, "time_anomalies": true, "security_warnings": true, "system_changes": true, "execution_counts": true}}, "alerts": {"enabled": true, "alert_file": "logs/security_alerts.json", "alert_conditions": {"security_score_below_threshold": true, "time_tampering_detected": true, "system_fingerprint_mismatch": true, "proof_chain_validation_failed": true, "execution_limit_exceeded": true}}, "maintenance": {"auto_cleanup": {"enabled": true, "cleanup_interval_hours": 168, "keep_logs_days": 30, "keep_proofs_days": 7}, "health_check": {"enabled": true, "check_interval_hours": 24, "components_to_check": ["time_sources", "system_fingerprint", "proof_chain_integrity", "storage_space"]}}, "compatibility": {"fallback_mode": {"enabled": true, "description": "当高级功能不可用时的降级模式", "conditions": {"missing_dependencies": true, "insufficient_permissions": true, "hardware_limitations": true}, "fallback_security_score": 30}, "legacy_support": {"old_license_format": true, "migration_assistance": true}}, "deployment": {"environment_detection": {"auto_detect_offline": true, "network_check_timeout_seconds": 5, "fallback_to_offline": true}, "installation": {"create_config_dirs": true, "initialize_time_anchors": true, "generate_system_fingerprint": true, "setup_logging": true}}}
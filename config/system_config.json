{
  "version": "1.0",
  "created_at": "2024-01-01T00:00:00Z",
  "description": "系统级安全配置，用户不应修改",
  "core_security_components": {
    "time_proof_chain": {
      "enabled": true,
      "mandatory": true,
      "min_chain_length": 1
    },
    "system_fingerprint": {
      "enabled": true,
      "mandatory": true,
      "min_similarity": 0.7
    },
    "anomaly_detection": {
      "enabled": true,
      "mandatory": true,
      "max_anomaly_score": 50
    }
  },
  "security_enforcement": {
    "min_security_score_absolute": 50,
    "max_user_configurable_score": 80,
    "mandatory_checks": [
      "time_validation",
      "system_integrity",
      "proof_chain_validation"
    ]
  }
}
# INTEGRITY_HASH: c74faaadb9926128fa4054bb1611cce2111c821277171f03e43aa3da57c1cf60
# SIGNATURE: e589bd084bb3622687c6e2b8ec526923971e718856194cb22f7bfdb21e955594
import click
import re
from typing import List, Optional, Tuple
from pathlib import Path
from collections import Counter
import math

from core.registry import registry
from core.exceptions import *

@click.command()
@click.argument('input_file', type=click.Path(exists=True, readable=True))
@click.option('-o', '--output', 'output_file',
              type=click.Path(writable=True),
              help='输出摘要到文件')
@click.option('-l', '--length', default=3, type=int,
              help='摘要长度（句子数量）')
@click.option('--method', default='frequency',
              type=click.Choice(['frequency', 'position', 'hybrid']),
              help='摘要算法：频率、位置或混合')
@click.option('--min-sentence-length', default=10, type=int,
              help='最小句子长度（字符数）')
@handle_exception
def summarize_file(input_file: str, output_file: Optional[str],
                   length: int, method: str, min_sentence_length: int):
    """文本摘要功能 - 从文本文件生成摘要"""

    click.echo(f"开始处理文件: {input_file}")

    # 读取文件内容
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
    except Exception as e:
        raise FileProcessingError(f"读取文件失败: {str(e)}")

    if not content:
        raise ValidationError("文件内容", "", "文件为空")

    # 生成摘要
    summary = generate_summary(content, length, method, min_sentence_length)

    if not summary:
        click.echo(click.style("警告: 无法生成摘要，文本可能太短", fg='yellow'))
        return

    # 显示结果
    click.echo(click.style("=== 文本摘要 ===", fg='blue', bold=True))
    click.echo(f"原文长度: {len(content)} 字符")
    click.echo(f"摘要方法: {method}")
    click.echo(f"摘要长度: {len(summary)} 句")
    click.echo()

    for i, sentence in enumerate(summary, 1):
        click.echo(f"{i}. {sentence}")

    # 保存到文件
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"原文件: {input_file}\n")
                f.write(f"摘要方法: {method}\n")
                f.write(f"摘要长度: {len(summary)} 句\n")
                f.write("=" * 50 + "\n\n")

                for i, sentence in enumerate(summary, 1):
                    f.write(f"{i}. {sentence}\n")

            click.echo(click.style(f"摘要已保存到: {output_file}", fg='green'))
        except Exception as e:
            raise FileProcessingError(f"保存文件失败: {str(e)}")

    return summary

def generate_summary(text: str, length: int, method: str, min_length: int) -> List[str]:
    """生成文本摘要"""

    # 分句
    sentences = split_sentences(text)

    # 过滤短句
    sentences = [s for s in sentences if len(s.strip()) >= min_length]

    if len(sentences) <= length:
        return sentences

    # 根据方法选择句子
    if method == 'frequency':
        return frequency_based_summary(sentences, length)
    elif method == 'position':
        return position_based_summary(sentences, length)
    elif method == 'hybrid':
        return hybrid_summary(sentences, length)
    else:
        return sentences[:length]

def split_sentences(text: str) -> List[str]:
    """分句处理"""
    # 中英文句子分割
    sentence_endings = r'[.!?。！？；;]'
    sentences = re.split(sentence_endings, text)

    # 清理和过滤
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 5:  # 过滤太短的句子
            cleaned_sentences.append(sentence)

    return cleaned_sentences

def frequency_based_summary(sentences: List[str], length: int) -> List[str]:
    """基于词频的摘要"""

    # 计算词频
    word_freq = Counter()
    for sentence in sentences:
        words = extract_words(sentence)
        word_freq.update(words)

    # 计算句子得分
    sentence_scores = []
    for i, sentence in enumerate(sentences):
        words = extract_words(sentence)
        score = sum(word_freq[word] for word in words)
        sentence_scores.append((score, i, sentence))

    # 选择得分最高的句子
    sentence_scores.sort(reverse=True)
    selected = sentence_scores[:length]

    # 按原文顺序排列
    selected.sort(key=lambda x: x[1])

    return [sentence for _, _, sentence in selected]

def position_based_summary(sentences: List[str], length: int) -> List[str]:
    """基于位置的摘要（开头和结尾优先）"""

    total = len(sentences)
    selected_indices = set()

    # 选择开头的句子
    start_count = min(length // 2 + 1, total // 3)
    for i in range(min(start_count, total)):
        selected_indices.add(i)

    # 选择结尾的句子
    end_count = length - len(selected_indices)
    for i in range(max(0, total - end_count), total):
        selected_indices.add(i)

    # 如果还需要更多句子，从中间选择
    while len(selected_indices) < length and len(selected_indices) < total:
        middle = total // 2
        for offset in range(total):
            idx1 = middle + offset
            idx2 = middle - offset
            if idx1 < total and idx1 not in selected_indices:
                selected_indices.add(idx1)
                break
            if idx2 >= 0 and idx2 not in selected_indices:
                selected_indices.add(idx2)
                break

    # 按顺序返回
    selected_indices = sorted(list(selected_indices))
    return [sentences[i] for i in selected_indices[:length]]

def hybrid_summary(sentences: List[str], length: int) -> List[str]:
    """混合方法摘要"""

    # 计算词频得分
    word_freq = Counter()
    for sentence in sentences:
        words = extract_words(sentence)
        word_freq.update(words)

    # 计算综合得分
    sentence_scores = []
    total_sentences = len(sentences)

    for i, sentence in enumerate(sentences):
        words = extract_words(sentence)

        # 词频得分
        freq_score = sum(word_freq[word] for word in words)

        # 位置得分（开头和结尾权重高）
        if i < total_sentences * 0.3:  # 前30%
            position_score = 2.0
        elif i > total_sentences * 0.7:  # 后30%
            position_score = 1.5
        else:  # 中间40%
            position_score = 1.0

        # 长度得分（适中长度优先）
        length_score = min(len(sentence) / 100, 1.0)

        # 综合得分
        total_score = freq_score * position_score * length_score
        sentence_scores.append((total_score, i, sentence))

    # 选择得分最高的句子
    sentence_scores.sort(reverse=True)
    selected = sentence_scores[:length]

    # 按原文顺序排列
    selected.sort(key=lambda x: x[1])

    return [sentence for _, _, sentence in selected]

def extract_words(text: str) -> List[str]:
    """提取单词（支持中英文）"""
    # 英文单词
    english_words = re.findall(r'\b[a-zA-Z]+\b', text.lower())

    # 中文字符（简单处理，实际可以使用jieba等分词工具）
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)

    # 过滤停用词
    stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                  'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}

    all_words = english_words + chinese_chars
    return [word for word in all_words if word not in stop_words and len(word) > 1]

# 注册模块
registry.register(
    'summarization',
    '文本摘要',
    '从文本文件生成智能摘要',
    summarize_file
)
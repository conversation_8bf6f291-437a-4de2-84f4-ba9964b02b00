import click
import re
from typing import Dict, Optional
from pathlib import Path

from core.registry import registry
from core.exceptions import *

# 简单的翻译词典（实际项目中可以使用更复杂的翻译服务）
TRANSLATION_DICT = {
    'en_to_zh': {
        'hello': '你好',
        'world': '世界',
        'good': '好的',
        'morning': '早上',
        'afternoon': '下午',
        'evening': '晚上',
        'night': '夜晚',
        'thank': '谢谢',
        'you': '你',
        'welcome': '欢迎',
        'please': '请',
        'sorry': '对不起',
        'yes': '是',
        'no': '不',
        'file': '文件',
        'data': '数据',
        'process': '处理',
        'complete': '完成',
        'error': '错误',
        'success': '成功',
    },
    'zh_to_en': {
        '你好': 'hello',
        '世界': 'world',
        '好的': 'good',
        '早上': 'morning',
        '下午': 'afternoon',
        '晚上': 'evening',
        '夜晚': 'night',
        '谢谢': 'thank you',
        '欢迎': 'welcome',
        '请': 'please',
        '对不起': 'sorry',
        '是': 'yes',
        '不': 'no',
        '文件': 'file',
        '数据': 'data',
        '处理': 'process',
        '完成': 'complete',
        '错误': 'error',
        '成功': 'success',
    }
}

@click.command()
@click.argument('text', required=False)
@click.option('-f', '--file', 'input_file',
              type=click.Path(exists=True, readable=True),
              help='从文件读取要翻译的文本')
@click.option('-o', '--output', 'output_file',
              type=click.Path(writable=True),
              help='输出翻译结果到文件')
@click.option('--source', default='auto',
              type=click.Choice(['auto', 'en', 'zh']),
              help='源语言')
@click.option('--target', default='zh',
              type=click.Choice(['en', 'zh']),
              help='目标语言')
@click.option('--mode', default='word',
              type=click.Choice(['word', 'sentence']),
              help='翻译模式：单词或句子')
@handle_exception
def translate(text: Optional[str], input_file: Optional[str],
              output_file: Optional[str], source: str, target: str, mode: str):
    """文本翻译功能 - 支持中英文互译"""

    # 获取要翻译的文本
    if input_file:
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                text_to_translate = f.read().strip()
            click.echo(f"从文件读取文本: {input_file}")
        except Exception as e:
            raise FileProcessingError(f"读取文件失败: {str(e)}")
    elif text:
        text_to_translate = text
    else:
        # 交互式输入
        text_to_translate = click.prompt("请输入要翻译的文本")

    if not text_to_translate:
        raise ValidationError("文本", "", "不能为空")

    # 自动检测源语言
    if source == 'auto':
        source = detect_language(text_to_translate)
        click.echo(f"检测到源语言: {source}")

    # 验证语言对
    if source == target:
        click.echo(click.style("源语言和目标语言相同，无需翻译", fg='yellow'))
        result = text_to_translate
    else:
        # 执行翻译
        result = perform_translation(text_to_translate, source, target, mode)

    # 显示结果
    click.echo(click.style("=== 翻译结果 ===", fg='blue', bold=True))
    click.echo(f"原文 ({source}): {text_to_translate}")
    click.echo(f"译文 ({target}): {result}")

    # 保存到文件
    if output_file:
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"原文 ({source}): {text_to_translate}\n")
                f.write(f"译文 ({target}): {result}\n")
            click.echo(click.style(f"翻译结果已保存到: {output_file}", fg='green'))
        except Exception as e:
            raise FileProcessingError(f"保存文件失败: {str(e)}")

    return result

def detect_language(text: str) -> str:
    """简单的语言检测"""
    # 检测中文字符
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    english_chars = len(re.findall(r'[a-zA-Z]', text))

    if chinese_chars > english_chars:
        return 'zh'
    else:
        return 'en'

def perform_translation(text: str, source: str, target: str, mode: str) -> str:
    """执行翻译"""
    translation_key = f"{source}_to_{target}"

    if translation_key not in TRANSLATION_DICT:
        raise ValidationError("语言对", f"{source} -> {target}", "不支持的翻译方向")

    word_dict = TRANSLATION_DICT[translation_key]

    if mode == 'word':
        # 单词翻译模式
        words = text.lower().split() if source == 'en' else list(text)
        translated_words = []

        for word in words:
            translated = word_dict.get(word.strip(), word)
            translated_words.append(translated)

        return ' '.join(translated_words) if target == 'en' else ''.join(translated_words)

    else:
        # 句子翻译模式（简单的单词替换）
        result = text
        for original, translation in word_dict.items():
            if source == 'en':
                # 英文单词边界匹配
                pattern = r'\b' + re.escape(original) + r'\b'
                result = re.sub(pattern, translation, result, flags=re.IGNORECASE)
            else:
                # 中文直接替换
                result = result.replace(original, translation)

        return result

# 注册模块
registry.register(
    'translation',
    '文本翻译',
    '支持中英文互译的文本翻译功能',
    translate
)
import pandas as pd
from pathlib import Path
from core.auth import require_license
from core.registry import registry

@require_license('data_cleaning')
def clean_data(input_file: str, output_file: str = None):
    """数据清理功能"""
    print(f"开始清理数据文件: {input_file}")
    
    try:
        # 读取数据
        if input_file.endswith('.csv'):
            df = pd.read_csv(input_file)
        elif input_file.endswith('.json'):
            df = pd.read_json(input_file)
        else:
            raise ValueError("不支持的文件格式")
        
        # 数据清理操作
        df_cleaned = df.dropna()
        df_cleaned = df_cleaned.drop_duplicates()
        
        # 保存清理后的数据
        if output_file:
            df_cleaned.to_csv(output_file, index=False)
            print(f"清理完成，结果保存到: {output_file}")
        
        print(f"清理统计: 原始行数 {len(df)}, 清理后行数 {len(df_cleaned)}")
        return df_cleaned
        
    except Exception as e:
        print(f"数据清理失败: {e}")
        return None

# 注册模块
registry.register(
    'data_cleaning',
    '数据清理',
    '清理和预处理数据文件，删除空值和重复项',
    clean_data
)
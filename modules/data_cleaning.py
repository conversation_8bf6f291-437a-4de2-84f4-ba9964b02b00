import pandas as pd
import click
from pathlib import Path
from typing import Optional

from core.registry import registry
from core.exceptions import *
@handle_exception
@click.command()
@click.argument('input_file', type=click.Path(exists=True, readable=True))
@click.option('-o', '--output', 'output_file',
              type=click.Path(writable=True),
              help='输出文件路径')
@click.option('--remove-duplicates/--keep-duplicates',
              default=True,
              help='是否删除重复行')
@click.option('--remove-na/--keep-na',
              default=True,
              help='是否删除空值行')
def clean_data(input_file: str, output_file: Optional[str],
               remove_duplicates: bool, remove_na: bool):
    """数据清理功能 - 清理和预处理数据文件"""

    click.echo(f"开始清理数据文件: {input_file}")

    # 检查文件格式
    input_path = Path(input_file)
    supported_formats = ['.csv', '.json', '.xlsx']

    if input_path.suffix.lower() not in supported_formats:
        raise UnsupportedFileFormatError(input_file, supported_formats)

    # 读取数据
    try:
        if input_path.suffix.lower() == '.csv':
            df = pd.read_csv(input_file)
        elif input_path.suffix.lower() == '.json':
            df = pd.read_json(input_file)
        elif input_path.suffix.lower() == '.xlsx':
            df = pd.read_excel(input_file)
    except Exception as e:
        raise FileProcessingError(f"读取文件失败: {str(e)}")

    original_rows = len(df)
    click.echo(f"原始数据行数: {original_rows}")

    # 数据清理操作
    if remove_na:
        df = df.dropna()
        click.echo(f"删除空值后行数: {len(df)}")

    if remove_duplicates:
        df = df.drop_duplicates()
        click.echo(f"删除重复行后行数: {len(df)}")

    # 保存清理后的数据
    if output_file:
        try:
            output_path = Path(output_file)
            if output_path.suffix.lower() == '.csv' or not output_path.suffix:
                if not output_path.suffix:
                    output_file = str(output_path.with_suffix('.csv'))
                df.to_csv(output_file, index=False)
            elif output_path.suffix.lower() == '.json':
                df.to_json(output_file, orient='records', indent=2)
            elif output_path.suffix.lower() == '.xlsx':
                df.to_excel(output_file, index=False)

            click.echo(click.style(f"清理完成，结果保存到: {output_file}", fg='green'))
        except Exception as e:
            raise FileProcessingError(f"保存文件失败: {str(e)}")
    else:
        click.echo("未指定输出文件，仅显示统计信息")

    # 显示清理统计
    cleaned_rows = len(df)
    removed_rows = original_rows - cleaned_rows
    removal_rate = (removed_rows / original_rows * 100) if original_rows > 0 else 0

    click.echo(click.style("=== 清理统计 ===", fg='blue', bold=True))
    click.echo(f"原始行数: {original_rows}")
    click.echo(f"清理后行数: {cleaned_rows}")
    click.echo(f"删除行数: {removed_rows}")
    click.echo(f"删除比例: {removal_rate:.2f}%")

    return df

# 注册模块
registry.register(
    'data_cleaning',
    '数据清理',
    '清理和预处理数据文件，删除空值和重复项',
    clean_data
)
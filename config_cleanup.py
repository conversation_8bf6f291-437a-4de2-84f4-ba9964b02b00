#!/usr/bin/env python3
"""
配置文件清理和验证脚本
清理冗余配置文件并验证新的硬编码配置架构
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def analyze_config_files():
    """分析当前配置文件状态"""
    print("=== 配置文件状态分析 ===")
    
    config_dir = Path("config")
    if not config_dir.exists():
        print("   ❌ config 目录不存在")
        return False
    
    # 检查现有配置文件
    config_files = {
        "app.license": "许可证文件",
        "user_config.json": "用户配置文件",
        "offline_security_config.json": "旧的离线安全配置（应删除）",
        "system_config.json": "系统配置文件（应删除）",
        ".security_policy": "安全策略文件"
    }
    
    print("   当前配置文件:")
    existing_files = []
    for file_name, description in config_files.items():
        file_path = config_dir / file_name
        if file_path.exists():
            existing_files.append(file_name)
            status = "✅ 存在"
        else:
            status = "❌ 不存在"
        print(f"     {file_name:<30} {status:<10} ({description})")
    
    return existing_files

def identify_redundant_files():
    """识别需要删除的冗余文件"""
    print("\n=== 冗余文件识别 ===")
    
    # 应该删除的文件列表
    files_to_remove = [
        "config/offline_security_config.json",  # 已被硬编码替代
        "config/system_config.json",            # 已被硬编码替代
    ]
    
    # 应该保留的文件列表
    files_to_keep = [
        "config/app.license",        # 许可证文件
        "config/user_config.json",   # 用户配置
        "config/.security_policy",   # 安全策略（如果存在）
    ]
    
    print("   需要删除的文件:")
    for file_path in files_to_remove:
        if Path(file_path).exists():
            print(f"     ❌ {file_path} - 已被硬编码替代")
        else:
            print(f"     ✅ {file_path} - 已删除")
    
    print("\n   需要保留的文件:")
    for file_path in files_to_keep:
        if Path(file_path).exists():
            print(f"     ✅ {file_path} - 保留")
        else:
            print(f"     ⚠️  {file_path} - 不存在")
    
    return files_to_remove, files_to_keep

def cleanup_redundant_files(files_to_remove, dry_run=True):
    """清理冗余文件"""
    print(f"\n=== 文件清理 ({'预览模式' if dry_run else '执行模式'}) ===")
    
    removed_files = []
    for file_path in files_to_remove:
        path = Path(file_path)
        if path.exists():
            if dry_run:
                print(f"   [预览] 将删除: {file_path}")
            else:
                try:
                    path.unlink()
                    removed_files.append(file_path)
                    print(f"   ✅ 已删除: {file_path}")
                except Exception as e:
                    print(f"   ❌ 删除失败: {file_path} - {e}")
        else:
            print(f"   ℹ️  文件不存在: {file_path}")
    
    if not dry_run and removed_files:
        print(f"\n   总计删除 {len(removed_files)} 个文件")
    
    return removed_files

def verify_hardcoded_config():
    """验证硬编码配置"""
    print("\n=== 硬编码配置验证 ===")
    
    try:
        from core.secure_config_manager import SecureConfigManager
        
        print("1. 创建配置管理器")
        manager = SecureConfigManager()
        
        print("2. 加载配置")
        config = manager.load_secure_config()
        
        print("3. 验证硬编码标识")
        is_hardcoded = config.get("hardcoded", False)
        if is_hardcoded:
            print("   ✅ 配置已硬编码")
        else:
            print("   ❌ 配置未硬编码")
            return False
        
        print("4. 验证核心安全组件")
        core_components = config.get("core_security_components", {})
        expected_components = ["time_proof_chain", "system_fingerprint", "anomaly_detection"]
        
        for component in expected_components:
            if component in core_components:
                enabled = core_components[component].get("enabled", False)
                mandatory = core_components[component].get("mandatory", False)
                if enabled and mandatory:
                    print(f"   ✅ {component}: 已启用且强制")
                else:
                    print(f"   ❌ {component}: 配置错误")
                    return False
            else:
                print(f"   ❌ 缺少组件: {component}")
                return False
        
        print("5. 验证安全约束")
        enforcement = config.get("security_enforcement", {})
        min_score = enforcement.get("min_security_score_absolute", 0)
        
        if min_score >= 50:
            print(f"   ✅ 最低安全分数: {min_score}")
        else:
            print(f"   ❌ 最低安全分数过低: {min_score}")
            return False
        
        print("6. 验证时间验证配置")
        time_validation = config.get("time_validation", {})
        time_sources = time_validation.get("time_sources", {})
        
        if len(time_sources) >= 3:
            print(f"   ✅ 时间源数量: {len(time_sources)}")
        else:
            print(f"   ⚠️  时间源数量较少: {len(time_sources)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def verify_user_config():
    """验证用户配置文件"""
    print("\n=== 用户配置验证 ===")
    
    user_config_file = Path("config/user_config.json")
    
    if not user_config_file.exists():
        print("   ⚠️  用户配置文件不存在")
        return True  # 用户配置是可选的
    
    try:
        with open(user_config_file, 'r', encoding='utf-8') as f:
            user_config = json.load(f)
        
        print("   ✅ 用户配置文件格式正确")
        
        # 检查是否包含安全提示
        if "_security_notice" in user_config:
            print("   ✅ 包含安全提示信息")
        
        # 检查允许修改的参数列表
        if "_allowed_modifications_info" in user_config:
            allowed_paths = user_config["_allowed_modifications_info"].get("allowed_paths", [])
            print(f"   ✅ 允许用户修改的参数: {len(allowed_paths)} 个")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"   ❌ 用户配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 用户配置验证失败: {e}")
        return False

def generate_cleanup_report():
    """生成清理报告"""
    print("\n=== 清理报告 ===")
    
    report = {
        "cleanup_date": "2024-01-01",
        "actions_taken": [
            "删除了 config/offline_security_config.json（已被硬编码替代）",
            "核心安全参数已硬编码到 core/secure_config_manager.py",
            "保留了 config/user_config.json 用于用户自定义配置",
            "移除了对外部系统配置文件的依赖"
        ],
        "security_improvements": [
            "核心安全参数无法被外部修改",
            "减少了配置文件攻击面",
            "提高了配置的完整性和可靠性",
            "简化了部署和维护流程"
        ],
        "remaining_files": [
            "config/app.license - 许可证文件",
            "config/user_config.json - 用户配置文件",
            "config/.security_policy - 安全策略文件（如果存在）"
        ]
    }
    
    print("   清理操作:")
    for action in report["actions_taken"]:
        print(f"     • {action}")
    
    print("\n   安全改进:")
    for improvement in report["security_improvements"]:
        print(f"     • {improvement}")
    
    print("\n   保留文件:")
    for file_info in report["remaining_files"]:
        print(f"     • {file_info}")
    
    return report

def main():
    """主函数"""
    print("配置文件清理和验证工具")
    print("=" * 60)
    
    # 1. 分析当前配置文件状态
    existing_files = analyze_config_files()
    
    # 2. 识别冗余文件
    files_to_remove, files_to_keep = identify_redundant_files()
    
    # 3. 预览清理操作
    cleanup_redundant_files(files_to_remove, dry_run=True)
    
    # 4. 询问是否执行清理
    if files_to_remove:
        response = input("\n是否执行文件清理？(y/N): ").strip().lower()
        if response == 'y':
            cleanup_redundant_files(files_to_remove, dry_run=False)
        else:
            print("   跳过文件清理")
    
    # 5. 验证硬编码配置
    hardcoded_ok = verify_hardcoded_config()
    
    # 6. 验证用户配置
    user_config_ok = verify_user_config()
    
    # 7. 生成清理报告
    generate_cleanup_report()
    
    # 8. 总结
    print("\n" + "=" * 60)
    print("清理和验证结果:")
    print(f"  硬编码配置: {'✅ 正常' if hardcoded_ok else '❌ 异常'}")
    print(f"  用户配置: {'✅ 正常' if user_config_ok else '❌ 异常'}")
    
    if hardcoded_ok and user_config_ok:
        print("\n🎉 配置系统已成功升级到硬编码架构！")
        print("   核心安全参数现在无法被外部修改，安全性大幅提升。")
    else:
        print("\n⚠️  配置系统存在问题，请检查上述错误信息。")

if __name__ == '__main__':
    main()
